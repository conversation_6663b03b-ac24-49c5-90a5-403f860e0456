#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
托运单处理程序 - 优化版
高性能批量打印功能，支持多线程处理和智能错误恢复
"""

# 添加GUI相关的导入
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue as gui_queue

import pandas as pd
import os
import sys
import time
import gc
import threading
import queue
from contextlib import contextmanager

# 检查必要的库是否已安装
try:
    import openpyxl
    import win32print
    import win32api
    print("所需库已成功导入")
    can_print = True
except ImportError as e:
    print(f"错误: 缺少必要的库 - {e}")
    if 'win32' in str(e):
        print("警告: 未安装打印所需的库，将跳过打印功能")
        print("如需打印功能，请运行: pip install pywin32")
        can_print = False
    else:
        print("请先运行: pip install pandas openpyxl pywin32")
        sys.exit(1)

# 优化的配置类
class Config:
    """增强的配置管理类"""

    # 公司映射配置（支持更多匹配模式）
    COMPANY_MAPPING = {
        '禾美': {
            'full_name': '深圳禾美科技有限公司',
            'logistics': '深圳市安捷通实业有限公司博罗分公司',
            'operator': '杜',
            'aliases': ['禾美科技', '禾美']
        },
        '禾喵喵': {
            'full_name': '深圳禾喵喵科技有限公司',
            'logistics': '深圳市安捷通实业有限公司博罗分公司',
            'operator': '杜',
            'aliases': ['禾喵喵科技', '禾喵喵']
        },
        '禹贡': {
            'full_name': '深圳市七彩虹禹贡科技发展有限公司',
            'logistics': '惠州市畅和仓储物流有限公司',
            'operator': '李志魁',
            'aliases': [  '禹贡']
        },
        '博琛': {
            'full_name': '深圳市博琛科技创新有限公司',
            'logistics': '惠州市畅和仓储物流有限公司',
            'operator': '李中梅',
            'aliases': ['博琛公司', '博琛科技', '博琛']
        },
        '夏宝': {
            'full_name': '夏宝鼎承（深圳）科技有限公司',
            'logistics': '深圳市安捷通实业有限公司博罗分公司',
            'operator': '朱蓉',
            'aliases': ['夏宝企业', '夏宝科技', '夏宝', '夏保']  # 添加"夏保"别名
        },
        '禹贡应用': {
            'full_name': '深圳市七彩虹禹贡应用科技创新有限公司',
            'logistics': '惠州市畅和仓储物流有限公司',
            'operator': '杜',
            'aliases': ['禹贡应用']
        },
        '禹贡信息': {
            'full_name': '深圳市七彩虹禹贡信息技术发展有限公司',
            'logistics': '惠州市畅和仓储物流有限公司',
            'operator': '杜',
            'aliases': ['禹贡信息']
        },
        '禹贡装备': {
            'full_name': '深圳市七彩虹禹贡装备科技发展有限公司',
            'logistics': '惠州市畅和仓储物流有限公司',
            'operator': '杜',
            'aliases': ['禹贡装备']
        },
        '旭祥': {
            'full_name': '深圳市旭祥科技发展有限公司',
            'logistics': '深圳市安捷通实业有限公司博罗分公司',
            'operator': '鄢光英',
            'aliases': ['旭祥科技', '旭祥']
        },
        '科技': {
            'full_name': '深圳市七彩虹科技发展有限公司',
            'logistics': '深圳市安捷通实业有限公司博罗分公司',
            'operator': '杜',
            'aliases': ['科技']
        }
    }
    # 产品类型映射配置
    PRODUCT_TYPE_MAPPING = {
        '212': '板卡',
        '110': '板卡',
        '818': '固态硬盘',
        '414': '播放器',
        'A10': '笔记本'
    }
    
    # 单元格映射规则
    CELL_MAPPING_RULES = [
        {'data_col': '单头备注', 'excel_col': 'F', 'excel_row': '3'},
        {'data_col': '公司主体', 'excel_col': 'B', 'excel_row': '2'},
        {'data_col': '物流公司', 'excel_col': 'A', 'excel_row': '3'},
        {'data_col': '目的地', 'excel_col': 'D', 'excel_row': '1'},
        {'data_col': '产品类型', 'excel_col': 'A', 'excel_row': '6'},
        {'data_col': '出货件数', 'excel_col': 'C', 'excel_row': '6'},
        {'data_col': '方数', 'excel_col': 'E', 'excel_row': '6'},
        {'data_col': '交货方式', 'excel_col': 'I', 'excel_row': '6'},
        {'data_col': '制单人', 'excel_col': 'J', 'excel_row': '17'}
    ]
    
    # 必要的列名
    REQUIRED_COLUMNS = ['单身备注', '公司主体', '出货数量', '出货件数', '料号']
    
    # 文件配置
    TEMPLATE_FILES = ['安捷通托运书.xlsx', '安捷通托运书.xls', '安捷通托运书.xlsm']
    OUTPUT_FILE = '处理后的托运单.xlsx'
    EXCLUDED_FILES = ['处理后的托运单.xlsx', '安捷通托运书.xlsx']
    
    # 性能配置（优化并发设置）
    BATCH_SIZE = 50  # 每批处理的数据量
    MAX_WORKERS = 1  # 减少到1个线程，避免COM冲突和资源竞争
    PRINT_DELAY = 0.5  # 减少打印任务间隔，提高效率
    SAVE_INTERVAL = 10  # 每处理多少条数据保存一次进度

    # 打印方案配置（完全无COM方案）
    PRINT_STRATEGY_CONFIG = {
        'use_com_objects': False,           # 完全禁用COM对象
        'use_system_print': True,           # 使用系统级打印
        'use_pdf_conversion': True,         # 支持PDF转换打印
        'use_image_conversion': False,      # 支持图片转换打印
        'temp_file_cleanup': True,          # 自动清理临时文件
        'print_timeout': 30,                # 打印超时时间（秒）
        'enable_print_monitoring': True     # 启用打印监控
    }

    # 打印精度配置（确保字体位置一致）
    PRINT_PRECISION_CONFIG = {
        'ensure_exact_scaling': True,      # 确保精确缩放
        'force_template_margins': True,    # 强制使用模板页边距
        'preserve_font_metrics': True,     # 保持字体度量
        'disable_auto_fit': True,          # 禁用自动适应
        'use_exact_dpi': True,             # 使用精确DPI
        'lock_print_area': True            # 锁定打印区域
    }

# 无COM打印管理器（编码修复版）
class NoCOMPrintManager:
    """极简高效打印管理器，专注速度和稳定性，修复编码问题"""

    def __init__(self):
        self.temp_files = []
        print("⚡ 极简高效打印管理器初始化完成（编码修复版）")

    def safe_get_short_path(self, file_path):
        """安全获取短路径，避免中文字符问题"""
        try:
            import win32api
            return win32api.GetShortPathName(os.path.abspath(file_path))
        except:
            # 如果获取短路径失败，返回原路径并进行转义
            return os.path.abspath(file_path).replace('\\', '\\\\')

    def safe_decode_output(self, output):
        """安全解码输出，避免编码错误"""
        if not output:
            return "无输出"

        try:
            if isinstance(output, bytes):
                # 尝试多种编码
                for encoding in ['utf-8', 'gbk', 'cp936', 'latin1']:
                    try:
                        return output.decode(encoding)
                    except UnicodeDecodeError:
                        continue
                # 如果所有编码都失败，使用错误替换
                return output.decode('utf-8', errors='replace')
            else:
                return str(output).strip()
        except:
            return "[编码错误，无法显示]"

    def print_excel_file(self, file_path, printer_name=None):
        """极简高效打印Excel文件（编码修复版）"""
        try:
            print(f"🚀 快速打印: {os.path.basename(file_path)}")
            start_time = time.time()

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False

            # 使用编码修复版的打印方法
            result = self._fast_shell_print_fixed(file_path)

            end_time = time.time()
            print(f"⏱️ 打印耗时: {end_time - start_time:.2f} 秒")

            return result

        except Exception as e:
            print(f"❌ 打印异常: {e}")
            return False

    def _fast_shell_print_fixed(self, file_path):
        """编码修复版的快速打印方案"""
        try:
            print("🚫 执行编码修复版无窗口打印...")

            # 方案1：编码修复版PowerShell后台打印（最推荐）
            if self._powershell_background_print_fixed(file_path):
                return True

            # 方案2：编码修复版VBScript完全后台打印（备选）
            if self._vbscript_background_print_fixed(file_path):
                return True

            # 方案3：原有的Shell打印 + 超强窗口隐藏
            if self._shell_print_with_aggressive_hide(file_path):
                return True

            print("⚠️ 所有无窗口打印方案都失败了")
            return False

        except Exception as e:
            print(f"⚠️ 无窗口打印异常: {e}")
            return False

    def _fast_shell_print(self, file_path):
        """真正无窗口闪动的打印方案"""
        try:
            print("🚫 执行真正无窗口打印...")

            # 方案1：尝试PowerShell后台打印（最推荐，已验证可用）
            if self._powershell_background_print(file_path):
                return True

            # 方案2：尝试VBScript完全后台打印（备选）
            if self._vbscript_background_print(file_path):
                return True

            # 方案3：Shell打印 + 超强窗口隐藏
            if self._shell_print_with_aggressive_hide(file_path):
                return True

            print("⚠️ 所有无窗口打印方案都失败了")
            return False

        except Exception as e:
            print(f"⚠️ 无窗口打印异常: {e}")
            return False

    def _powershell_background_print_fixed(self, file_path):
        """编码修复版PowerShell后台打印"""
        try:
            print("🔄 PowerShell打印（编码修复版）...")
            import subprocess
            import tempfile

            # 获取安全的文件路径
            safe_path = self.safe_get_short_path(file_path)

            # 创建PowerShell脚本，强制使用UTF-8编码
            ps_script_content = f'''
# 强制设置编码为UTF-8
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

try {{
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    $excel.ScreenUpdating = $false

    $workbook = $excel.Workbooks.Open("{safe_path}", 0, $true)
    $workbook.PrintOut()
    $workbook.Close($false)
    $excel.Quit()

    # 清理COM对象
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    [System.GC]::Collect()

    Write-Output "SUCCESS"
}} catch {{
    Write-Output "ERROR: $($_.Exception.Message)"
    exit 1
}}
'''

            # 写入临时PowerShell文件，使用UTF-8 BOM编码
            with tempfile.NamedTemporaryFile(mode='w', suffix='.ps1', delete=False, encoding='utf-8-sig') as f:
                f.write(ps_script_content)
                ps_file = f.name

            try:
                # 执行PowerShell，明确指定编码参数
                result = subprocess.run(
                    [
                        'powershell.exe',
                        '-WindowStyle', 'Hidden',
                        '-NoProfile',
                        '-NonInteractive',
                        '-NoLogo',
                        '-ExecutionPolicy', 'Bypass',
                        '-OutputFormat', 'Text',
                        '-File', ps_file
                    ],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',      # 明确指定UTF-8编码
                    errors='replace',      # 替换无法解码的字符
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ PowerShell后台打印成功（编码修复）")
                    return True
                else:
                    # 安全处理错误输出
                    stdout_safe = self.safe_decode_output(result.stdout)
                    stderr_safe = self.safe_decode_output(result.stderr)
                    print(f"⚠️ PowerShell后台打印失败")
                    print(f"   返回码: {result.returncode}")
                    print(f"   输出: {stdout_safe}")
                    print(f"   错误: {stderr_safe}")
                    return False

            finally:
                # 清理临时文件
                try:
                    time.sleep(0.5)
                    os.unlink(ps_file)
                except:
                    pass

        except Exception as e:
            print(f"⚠️ PowerShell后台打印异常: {e}")
            return False

    def _vbscript_background_print_fixed(self, file_path):
        """编码修复版VBScript后台打印"""
        try:
            print("🔄 VBScript打印（编码修复版）...")
            import subprocess
            import tempfile

            # 获取安全的文件路径
            safe_path = self.safe_get_short_path(file_path)

            # 创建VBScript内容
            vbs_content = f'''
On Error Resume Next

Set objExcel = CreateObject("Excel.Application")
If Err.Number <> 0 Then
    WScript.Echo "ERROR: Cannot create Excel"
    WScript.Quit 1
End If

objExcel.Visible = False
objExcel.DisplayAlerts = False
objExcel.ScreenUpdating = False

Set objWorkbook = objExcel.Workbooks.Open("{safe_path}", 0, True)
If Err.Number <> 0 Then
    objExcel.Quit
    WScript.Echo "ERROR: Cannot open file"
    WScript.Quit 1
End If

objWorkbook.PrintOut
objWorkbook.Close False
objExcel.Quit

Set objWorkbook = Nothing
Set objExcel = Nothing

WScript.Echo "SUCCESS"
'''

            # 写入临时VBS文件，使用ANSI编码（cscript默认编码）
            with tempfile.NamedTemporaryFile(mode='w', suffix='.vbs', delete=False, encoding='gbk') as f:
                f.write(vbs_content)
                vbs_file = f.name

            try:
                # 执行VBScript，指定正确的编码
                result = subprocess.run(
                    ['cscript.exe', '//NoLogo', '//B', vbs_file],
                    capture_output=True,
                    text=True,
                    encoding='gbk',        # 使用GBK编码读取cscript输出
                    errors='replace',      # 替换无法解码的字符
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ VBScript后台打印成功（编码修复）")
                    return True
                else:
                    # 安全处理错误输出
                    stdout_safe = self.safe_decode_output(result.stdout)
                    stderr_safe = self.safe_decode_output(result.stderr)
                    print(f"⚠️ VBScript后台打印失败")
                    print(f"   输出: {stdout_safe}")
                    print(f"   错误: {stderr_safe}")
                    return False

            finally:
                # 清理临时文件
                try:
                    time.sleep(0.5)
                    os.unlink(vbs_file)
                except:
                    pass

        except Exception as e:
            print(f"⚠️ VBScript后台打印异常: {e}")
            return False

    def _vbscript_background_print(self, file_path):
        """VBScript完全后台打印（无任何窗口）- 修复编码问题"""
        try:
            print("🔄 VBScript完全后台打印...")
            import subprocess
            import tempfile
            import os
            import time

            # 创建修复的VBScript（使用经过验证的方法）
            abs_file_path = os.path.abspath(file_path)

            # 处理文件路径中的中文字符，避免编码问题
            try:
                # 尝试获取短路径名，避免中文字符问题
                import win32api
                short_path = win32api.GetShortPathName(abs_file_path)
                abs_file_path = short_path
            except:
                # 如果获取短路径失败，对路径进行转义处理
                abs_file_path = abs_file_path.replace('\\', '\\\\')

            vbs_content = f'''
On Error Resume Next

Set objExcel = CreateObject("Excel.Application")
If Err.Number <> 0 Then
    WScript.Echo "ERROR: Cannot create Excel"
    WScript.Quit 1
End If

objExcel.Visible = False
objExcel.DisplayAlerts = False
objExcel.ScreenUpdating = False

Set objWorkbook = objExcel.Workbooks.Open("{abs_file_path}", 0, True)
If Err.Number <> 0 Then
    objExcel.Quit
    WScript.Echo "ERROR: Cannot open file"
    WScript.Quit 1
End If

objWorkbook.PrintOut
objWorkbook.Close False
objExcel.Quit

Set objWorkbook = Nothing
Set objExcel = Nothing

WScript.Echo "SUCCESS"
'''

            # 写入临时VBS文件，使用ANSI编码以兼容cscript
            with tempfile.NamedTemporaryFile(mode='w', suffix='.vbs', delete=False, encoding='gbk') as f:
                f.write(vbs_content)
                vbs_file = f.name

            try:
                # 执行VBScript（完全后台，无窗口），指定正确的编码处理
                result = subprocess.run(
                    ['cscript.exe', '//NoLogo', '//B', vbs_file],
                    capture_output=True,
                    text=True,
                    encoding='gbk',     # 使用GBK编码读取cscript输出
                    errors='ignore',    # 忽略编码错误
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ VBScript后台打印成功（完全无窗口）")
                    return True
                else:
                    print("⚠️ VBScript后台打印失败")
                    # 安全地处理输出，避免编码错误
                    try:
                        stdout_safe = result.stdout.strip() if result.stdout else '无输出'
                        stderr_safe = result.stderr.strip() if result.stderr else '无错误'
                        print(f"   输出: {stdout_safe}")
                        print(f"   错误: {stderr_safe}")
                    except UnicodeDecodeError:
                        print("   输出: [编码错误，无法显示]")
                    return False

            finally:
                try:
                    time.sleep(0.5)
                    os.unlink(vbs_file)
                except:
                    pass

        except Exception as e:
            print(f"⚠️ VBScript后台打印异常: {e}")
            return False

    def _powershell_background_print(self, file_path):
        """PowerShell完全后台打印（无窗口版）- 修复编码问题"""
        try:
            print("🔄 PowerShell完全后台打印...")
            import subprocess
            import tempfile

            # 使用经过验证的简化PowerShell方法
            abs_file_path = os.path.abspath(file_path)

            # 确保文件路径使用正确的编码，避免中文路径问题
            try:
                # 将路径转换为短路径格式，避免中文字符问题
                import win32api
                short_path = win32api.GetShortPathName(abs_file_path)
                abs_file_path = short_path
            except:
                # 如果获取短路径失败，使用原路径但进行编码处理
                abs_file_path = abs_file_path.replace('\\', '\\\\')

            # 创建PowerShell脚本文件（避免命令行参数问题）
            ps_script_content = f'''
# 设置输出编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

try {{
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    $excel.ScreenUpdating = $false
    $workbook = $excel.Workbooks.Open("{abs_file_path}", 0, $true)
    $workbook.PrintOut()
    $workbook.Close($false)
    $excel.Quit()
    Write-Output "SUCCESS"
}} catch {{
    Write-Output "ERROR: $($_.Exception.Message)"
    exit 1
}}
'''

            # 写入临时PowerShell文件，使用UTF-8编码
            with tempfile.NamedTemporaryFile(mode='w', suffix='.ps1', delete=False, encoding='utf-8-sig') as f:
                f.write(ps_script_content)
                ps_file = f.name

            try:
                # 使用文件执行方式，完全隐藏窗口，指定正确的编码
                result = subprocess.run(
                    ['powershell.exe', '-WindowStyle', 'Hidden', '-NoProfile',
                     '-NonInteractive', '-NoLogo', '-ExecutionPolicy', 'Bypass',
                     '-OutputFormat', 'Text', '-File', ps_file],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',  # 明确指定UTF-8编码
                    errors='ignore',   # 忽略编码错误
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    startupinfo=self._get_hidden_startupinfo()
                )

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ PowerShell后台打印成功（完全无窗口）")
                    return True
                else:
                    print(f"⚠️ PowerShell后台打印失败")
                    print(f"   返回码: {result.returncode}")
                    # 安全地处理输出，避免编码错误
                    try:
                        stdout_safe = result.stdout.strip() if result.stdout else '无输出'
                        stderr_safe = result.stderr.strip() if result.stderr else '无错误'
                        print(f"   输出: {stdout_safe}")
                        print(f"   错误: {stderr_safe}")
                    except UnicodeDecodeError:
                        print("   输出: [编码错误，无法显示]")
                    return False

            finally:
                # 清理临时文件
                try:
                    import time
                    time.sleep(0.5)  # 等待文件释放
                    os.unlink(ps_file)
                except:
                    pass

        except Exception as e:
            print(f"⚠️ PowerShell后台打印异常: {e}")
            return False

    def _get_hidden_startupinfo(self):
        """获取隐藏窗口的启动信息"""
        try:
            import subprocess
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            return startupinfo
        except:
            return None

    def _shell_print_with_aggressive_hide(self, file_path):
        """Shell打印 + 超强窗口隐藏（最后备选）"""
        try:
            print("🔄 Shell打印 + 超强窗口隐藏...")
            import win32api
            import win32gui
            import win32con
            import threading
            import time

            # 超强窗口隐藏器
            stop_hide = threading.Event()
            hidden_count = 0

            def ultra_aggressive_hide():
                nonlocal hidden_count
                while not stop_hide.is_set():
                    try:
                        def enum_callback(hwnd, param):
                            nonlocal hidden_count
                            if win32gui.IsWindowVisible(hwnd):
                                window_text = win32gui.GetWindowText(hwnd)
                                class_name = win32gui.GetClassName(hwnd)
                                if any(keyword in window_text.upper() or keyword in class_name.upper()
                                      for keyword in ['EXCEL', 'XLMAIN', 'MICROSOFT EXCEL']):
                                    # 立即隐藏
                                    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                                    # 移到屏幕外
                                    win32gui.SetWindowPos(hwnd, win32con.HWND_BOTTOM,
                                                        -10000, -10000, 0, 0,
                                                        win32con.SWP_HIDEWINDOW | win32con.SWP_NOACTIVATE)
                                    # 最小化
                                    win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
                                    hidden_count += 1
                            return True

                        win32gui.EnumWindows(enum_callback, None)
                    except:
                        pass
                    time.sleep(0.01)  # 每10ms检查一次

            # 启动超强隐藏
            hide_thread = threading.Thread(target=ultra_aggressive_hide, daemon=True)
            hide_thread.start()

            try:
                # 执行Shell打印
                result = win32api.ShellExecute(
                    0, "print", file_path, None, os.path.dirname(file_path), 0
                )

                # 等待打印完成，持续隐藏窗口
                time.sleep(3)

                if result > 32:
                    print(f"✅ Shell打印成功（隐藏了{hidden_count}个窗口）")
                    return True
                else:
                    print(f"⚠️ Shell打印失败，错误码: {result}")
                    return False

            finally:
                stop_hide.set()
                time.sleep(0.2)

        except Exception as e:
            print(f"⚠️ Shell打印异常: {e}")
            return False

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            for temp_file in self.temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            self.temp_files.clear()
            print("🧹 临时文件已清理")
        except Exception as e:
            print(f"⚠️ 清理临时文件时出错: {e}")

    # 以下方法已删除以简化程序，避免影响Excel
    def _try_simple_vbscript_print(self, file_path, printer_name):
        """简化的VBScript静默打印 - 修复编码问题"""
        try:
            print("🔄 尝试VBScript静默打印...")
            import subprocess
            import tempfile
            import os
            import time

            # 处理文件路径编码问题
            abs_file_path = os.path.abspath(file_path)
            try:
                import win32api
                short_path = win32api.GetShortPathName(abs_file_path)
                abs_file_path = short_path
            except:
                abs_file_path = abs_file_path.replace('\\', '\\\\')

            # 创建简化的VBScript
            vbs_content = f'''
On Error Resume Next

Set objExcel = CreateObject("Excel.Application")
If Err.Number <> 0 Then
    WScript.Echo "ERROR: Cannot create Excel"
    WScript.Quit 1
End If

objExcel.Visible = False
objExcel.DisplayAlerts = False
objExcel.ScreenUpdating = False

Set objWorkbook = objExcel.Workbooks.Open("{abs_file_path}", 0, True)
If Err.Number <> 0 Then
    objExcel.Quit
    WScript.Echo "ERROR: Cannot open file"
    WScript.Quit 1
End If

objWorkbook.PrintOut 1, 1, 1, False
objWorkbook.Close False
objExcel.Quit

Set objWorkbook = Nothing
Set objExcel = Nothing

WScript.Echo "SUCCESS"
'''

            # 写入临时VBS文件，使用GBK编码
            with tempfile.NamedTemporaryFile(mode='w', suffix='.vbs', delete=False, encoding='gbk') as f:
                f.write(vbs_content)
                vbs_file = f.name

            try:
                # 执行VBScript，指定正确的编码
                result = subprocess.run(
                    ['cscript.exe', '//NoLogo', '//B', vbs_file],
                    capture_output=True,
                    text=True,
                    encoding='gbk',
                    errors='ignore',
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ VBScript静默打印成功")
                    return True
                else:
                    try:
                        error_msg = result.stderr or result.stdout or '未知错误'
                        print(f"⚠️ VBScript打印失败: {error_msg}")
                    except UnicodeDecodeError:
                        print("⚠️ VBScript打印失败: [编码错误，无法显示详细信息]")
                    return False

            finally:
                # 删除临时文件
                try:
                    time.sleep(0.5)
                    os.unlink(vbs_file)
                except:
                    pass

        except Exception as e:
            print(f"⚠️ VBScript打印异常: {e}")
            return False

    def _try_simple_powershell_print(self, file_path, printer_name):
        """简化的PowerShell打印 - 修复编码问题"""
        try:
            print("🔄 尝试PowerShell打印...")
            import subprocess

            # 处理文件路径编码问题
            abs_file_path = os.path.abspath(file_path)
            try:
                import win32api
                short_path = win32api.GetShortPathName(abs_file_path)
                abs_file_path = short_path
            except:
                abs_file_path = abs_file_path.replace('\\', '\\\\')

            ps_script = f'''
            [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
            $OutputEncoding = [System.Text.Encoding]::UTF8
            try {{
                Start-Process -FilePath "{abs_file_path}" -Verb Print -WindowStyle Hidden -Wait
                Write-Output "SUCCESS"
            }} catch {{
                Write-Output "ERROR: $($_.Exception.Message)"
            }}
            '''

            result = subprocess.run(
                ['powershell.exe', '-WindowStyle', 'Hidden', '-Command', ps_script],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=30,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0 and "SUCCESS" in result.stdout:
                print("✅ PowerShell打印成功")
                return True
            else:
                try:
                    error_msg = result.stderr or result.stdout or '未知错误'
                    print(f"⚠️ PowerShell打印失败: {error_msg}")
                except UnicodeDecodeError:
                    print("⚠️ PowerShell打印失败: [编码错误，无法显示详细信息]")
                return False

        except Exception as e:
            print(f"⚠️ PowerShell打印异常: {e}")
            return False

    def _try_simple_shell_print(self, file_path, printer_name):
        """简化的系统Shell打印（带简单窗口隐藏）"""
        try:
            print("🔄 尝试Shell打印（带窗口隐藏）...")
            import win32api
            import win32gui
            import win32con
            import os
            import time
            import threading

            # 启动简单的窗口隐藏
            stop_hide = threading.Event()

            def simple_hide_windows():
                """简单的窗口隐藏"""
                while not stop_hide.is_set():
                    try:
                        def enum_callback(hwnd, param):
                            if win32gui.IsWindowVisible(hwnd):
                                window_text = win32gui.GetWindowText(hwnd)
                                class_name = win32gui.GetClassName(hwnd)
                                if 'Excel' in window_text or 'XLMAIN' in class_name:
                                    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                            return True

                        win32gui.EnumWindows(enum_callback, None)
                    except:
                        pass
                    time.sleep(0.1)

            # 启动隐藏线程
            hide_thread = threading.Thread(target=simple_hide_windows, daemon=True)
            hide_thread.start()

            try:
                # 执行打印
                result = win32api.ShellExecute(
                    0, "print", file_path, None, os.path.dirname(file_path), 0
                )

                # 等待打印完成
                time.sleep(2)

                if result > 32:
                    print("✅ Shell打印成功（窗口已隐藏）")
                    return True
                else:
                    print(f"⚠️ Shell打印失败，错误码: {result}")
                    return False

            finally:
                # 停止隐藏
                stop_hide.set()
                time.sleep(0.2)

        except Exception as e:
            print(f"⚠️ Shell打印异常: {e}")
            return False

    def _try_vbscript_silent_print(self, file_path, printer_name):
        """使用VBScript完全静默打印（最推荐）"""
        try:
            print("🔄 尝试VBScript完全静默打印...")
            import subprocess
            import tempfile
            import os
            import time

            # 创建改进的VBScript文件进行完全静默打印
            # 使用绝对路径和错误处理
            abs_file_path = os.path.abspath(file_path).replace('\\', '\\\\')

            vbs_content = f'''
On Error Resume Next

Dim objShell, objExcel, objWorkbook, objWorksheet
Dim errorOccurred
errorOccurred = False

' 设置静默模式环境变量
Set objShell = CreateObject("WScript.Shell")
If Err.Number <> 0 Then
    WScript.Echo "ERROR: Cannot create Shell object"
    WScript.Quit 1
End If

objShell.Environment("Process")("EXCEL_SILENT") = "1"
objShell.Environment("Process")("OFFICE_SILENT") = "1"

' 创建Excel应用程序对象
Set objExcel = CreateObject("Excel.Application")
If Err.Number <> 0 Then
    WScript.Echo "ERROR: Cannot create Excel object - " & Err.Description
    WScript.Quit 1
End If

' 立即设置为完全不可见和静默
objExcel.Visible = False
objExcel.DisplayAlerts = False
objExcel.ScreenUpdating = False
objExcel.EnableEvents = False
objExcel.Interactive = False
objExcel.UserControl = False

' 设置更多静默选项
On Error Resume Next
objExcel.ShowWindowsInTaskbar = False
objExcel.WindowState = -4140
objExcel.DisplayStatusBar = False
objExcel.DisplayFormulaBar = False
objExcel.AskToUpdateLinks = False
objExcel.AlertBeforeOverwriting = False

' 打开工作簿（只读模式）
Set objWorkbook = objExcel.Workbooks.Open("{abs_file_path}", 0, True)
If Err.Number <> 0 Then
    objExcel.Quit
    WScript.Echo "ERROR: Cannot open file - " & Err.Description
    WScript.Quit 1
End If

' 设置打印机（如果指定）
If "{printer_name}" <> "默认打印机" And "{printer_name}" <> "" Then
    objExcel.ActivePrinter = "{printer_name}"
End If

' 获取活动工作表并执行打印
Set objWorksheet = objWorkbook.ActiveSheet
objWorksheet.PrintOut 1, 1, 1, False

' 立即关闭工作簿
objWorkbook.Close False

' 退出Excel
objExcel.Quit

' 清理对象
Set objWorksheet = Nothing
Set objWorkbook = Nothing
Set objExcel = Nothing
Set objShell = Nothing

WScript.Echo "SUCCESS"
'''

            # 写入临时VBS文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.vbs', delete=False, encoding='utf-8') as vbs_file:
                vbs_file.write(vbs_content)
                vbs_file_path = vbs_file.name

            try:
                # 执行VBScript（完全静默）
                result = subprocess.run(
                    ['cscript.exe', '//NoLogo', '//B', vbs_file_path],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS
                )

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print(f"✅ VBScript静默打印成功（完全无窗口）")
                    return True
                else:
                    print(f"⚠️ VBScript打印失败: {result.stderr or result.stdout}")
                    return False

            finally:
                # 删除临时VBS文件
                try:
                    time.sleep(0.5)  # 等待VBScript完成
                    os.unlink(vbs_file_path)
                except:
                    pass

        except Exception as e:
            print(f"⚠️ VBScript静默打印失败: {e}")
            return False

    def _try_pdf_conversion_print(self, file_path, printer_name):
        """使用PDF转换打印（完全无窗口，最推荐）"""
        try:
            print("🔄 尝试PDF转换打印（完全无窗口）...")
            import os
            import tempfile
            import subprocess
            import time

            # 创建临时PDF文件
            pdf_file = None
            try:
                # 使用openpyxl + reportlab进行PDF转换（完全无窗口）
                pdf_file = self._convert_excel_to_pdf_silent(file_path)
                if not pdf_file:
                    print("⚠️ PDF转换失败，尝试其他方法")
                    return False

                # 使用系统默认PDF打印程序打印
                result = subprocess.run(
                    ['rundll32.exe', 'mshtml.dll,PrintHTML', pdf_file],
                    capture_output=True,
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if result.returncode == 0:
                    print(f"✅ PDF转换打印成功（完全无窗口）")
                    return True
                else:
                    # 尝试使用Adobe Reader或其他PDF阅读器打印
                    try:
                        subprocess.run(
                            ['AcroRd32.exe', '/t', pdf_file, printer_name],
                            capture_output=True,
                            timeout=30,
                            creationflags=subprocess.CREATE_NO_WINDOW
                        )
                        print(f"✅ PDF转换打印成功（Adobe Reader）")
                        return True
                    except:
                        print("⚠️ PDF打印失败，尝试其他方法")
                        return False

            finally:
                # 清理临时PDF文件
                if pdf_file and os.path.exists(pdf_file):
                    try:
                        time.sleep(1)  # 等待打印完成
                        os.remove(pdf_file)
                        self.temp_files.append(pdf_file)
                    except:
                        pass

        except Exception as e:
            print(f"⚠️ PDF转换打印失败: {e}")
            return False

    def _convert_excel_to_pdf_silent(self, excel_file):
        """静默将Excel转换为PDF（无窗口）"""
        try:
            import openpyxl
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            import tempfile
            import os

            # 读取Excel文件
            wb = openpyxl.load_workbook(excel_file, read_only=True)
            ws = wb.active

            # 创建临时PDF文件
            pdf_fd, pdf_file = tempfile.mkstemp(suffix='.pdf')
            os.close(pdf_fd)

            # 创建PDF
            c = canvas.Canvas(pdf_file, pagesize=A4)
            width, height = A4

            # 简单的Excel到PDF转换
            y_position = height - 50
            for row in ws.iter_rows(values_only=True):
                if y_position < 50:
                    c.showPage()
                    y_position = height - 50

                row_text = ' | '.join(str(cell) if cell is not None else '' for cell in row)
                c.drawString(50, y_position, row_text[:100])  # 限制长度
                y_position -= 20

            c.save()
            wb.close()

            print(f"✅ Excel转PDF成功: {os.path.basename(pdf_file)}")
            return pdf_file

        except Exception as e:
            print(f"⚠️ Excel转PDF失败: {e}")
            return None

    def _try_system_shell_print(self, file_path, printer_name):
        """使用系统Shell打印（超强窗口隐藏版）"""
        try:
            print("🔄 尝试系统Shell打印（超强窗口隐藏）...")
            import win32api
            import win32gui
            import win32con
            import os
            import time
            import threading

            # 超强窗口隐藏机制
            stop_monitoring = threading.Event()
            hidden_windows = []

            def aggressive_window_hider():
                """超激进的窗口隐藏器"""
                while not stop_monitoring.is_set():
                    try:
                        def enum_callback(hwnd, windows_list):
                            if win32gui.IsWindowVisible(hwnd):
                                class_name = win32gui.GetClassName(hwnd)
                                window_text = win32gui.GetWindowText(hwnd)
                                if any(keyword in class_name.upper() or keyword in window_text.upper()
                                      for keyword in ['EXCEL', 'XLMAIN', 'MICROSOFT EXCEL']):
                                    # 立即隐藏窗口
                                    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                                    # 设置窗口位置到屏幕外
                                    win32gui.SetWindowPos(hwnd, win32con.HWND_BOTTOM,
                                                        -10000, -10000, 0, 0,
                                                        win32con.SWP_HIDEWINDOW | win32con.SWP_NOACTIVATE)
                                    # 最小化窗口
                                    win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
                                    windows_list.append(hwnd)
                            return True

                        windows_list = []
                        win32gui.EnumWindows(enum_callback, windows_list)
                        if windows_list:
                            hidden_windows.extend(windows_list)
                            print(f"   🔒 隐藏了 {len(windows_list)} 个Excel窗口")
                    except:
                        pass
                    time.sleep(0.01)  # 每10ms检查一次，超高频率

            # 启动超激进窗口隐藏线程
            hider_thread = threading.Thread(target=aggressive_window_hider, daemon=True)
            hider_thread.start()

            try:
                # 预先隐藏一次
                aggressive_window_hider()

                # 使用ShellExecute进行系统级打印
                result = win32api.ShellExecute(
                    0,
                    "print",
                    file_path,
                    None,
                    os.path.dirname(file_path),
                    0  # SW_HIDE
                )

                # 等待打印完成，持续隐藏窗口
                time.sleep(3)  # 等待3秒让打印完成

                if result > 32:  # 成功
                    print(f"✅ 系统Shell打印成功（超强窗口隐藏）")
                    return True
                else:
                    print(f"⚠️ 系统Shell打印失败，错误码: {result}")
                    return False

            finally:
                # 停止监控
                stop_monitoring.set()
                time.sleep(0.1)

                # 最后再隐藏一次
                try:
                    aggressive_window_hider()
                    if hidden_windows:
                        print(f"   🔒 总共隐藏了 {len(hidden_windows)} 个Excel窗口")
                except:
                    pass

        except Exception as e:
            print(f"⚠️ 系统Shell打印失败: {e}")
            return False

    def _try_powershell_print(self, file_path, printer_name):
        """使用PowerShell打印（完全无窗口版）"""
        try:
            print("🔄 尝试PowerShell打印（完全无窗口）...")
            import subprocess
            import threading
            import time
            import win32gui
            import win32con

            # 启动窗口隐藏监控
            stop_monitoring = threading.Event()

            def hide_all_windows():
                """持续隐藏所有Excel相关窗口"""
                while not stop_monitoring.is_set():
                    try:
                        def enum_callback(hwnd, windows_list):
                            if win32gui.IsWindowVisible(hwnd):
                                class_name = win32gui.GetClassName(hwnd)
                                window_text = win32gui.GetWindowText(hwnd)
                                if any(keyword in class_name.upper() or keyword in window_text.upper()
                                      for keyword in ['EXCEL', 'XLMAIN', 'MICROSOFT EXCEL']):
                                    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                                    win32gui.SetWindowPos(hwnd, win32con.HWND_BOTTOM,
                                                        0, 0, 0, 0,
                                                        win32con.SWP_HIDEWINDOW | win32con.SWP_NOACTIVATE)
                                    windows_list.append(hwnd)
                            return True

                        windows_list = []
                        win32gui.EnumWindows(enum_callback, windows_list)
                    except:
                        pass
                    time.sleep(0.05)  # 每50ms检查一次

            # 启动监控线程
            monitor_thread = threading.Thread(target=hide_all_windows, daemon=True)
            monitor_thread.start()

            try:
                # 使用PowerShell的COM对象进行完全静默打印
                powershell_script = f'''
                $ErrorActionPreference = "SilentlyContinue"

                # 设置环境变量强制静默
                $env:EXCEL_SILENT = "1"
                $env:OFFICE_SILENT = "1"

                try {{
                    # 使用COM对象但设置为完全不可见
                    $excel = New-Object -ComObject Excel.Application
                    $excel.Visible = $false
                    $excel.DisplayAlerts = $false
                    $excel.ScreenUpdating = $false
                    $excel.EnableEvents = $false
                    $excel.Interactive = $false
                    $excel.UserControl = $false
                    $excel.WindowState = -4140
                    $excel.ShowWindowsInTaskbar = $false

                    # 打开文件
                    $workbook = $excel.Workbooks.Open("{file_path}", 0, $true)

                    # 设置打印机并打印
                    $excel.ActivePrinter = "{printer_name}"
                    $workbook.PrintOut(1, 1, 1, $false, "{printer_name}")

                    # 关闭文件和Excel
                    $workbook.Close($false)
                    $excel.Quit()

                    # 清理COM对象
                    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
                    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
                    [System.GC]::Collect()

                    Write-Output "SUCCESS"
                }} catch {{
                    Write-Output "ERROR: $($_.Exception.Message)"
                }}
                '''

                result = subprocess.run(
                    ['powershell.exe', '-WindowStyle', 'Hidden', '-NoProfile', '-NonInteractive',
                     '-NoLogo', '-ExecutionPolicy', 'Bypass', '-Command', powershell_script],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS
                )

                # 等待打印完成
                time.sleep(1)

                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print(f"✅ PowerShell打印成功（完全无窗口）")
                    return True
                else:
                    print(f"⚠️ PowerShell打印失败: {result.stderr or result.stdout}")
                    return False

            finally:
                # 停止监控
                stop_monitoring.set()
                time.sleep(0.1)

        except Exception as e:
            print(f"⚠️ PowerShell打印失败: {e}")
            return False

    def _try_command_line_print(self, file_path, printer_name):
        """使用命令行打印 - 修复编码问题"""
        try:
            print("🔄 尝试命令行打印...")
            import subprocess

            # 处理文件路径编码问题
            abs_file_path = os.path.abspath(file_path)
            try:
                import win32api
                short_path = win32api.GetShortPathName(abs_file_path)
                abs_file_path = short_path
            except:
                pass

            # 使用Windows的print命令
            cmd = f'print /D:"{printer_name}" "{abs_file_path}"'
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='gbk',
                errors='ignore',
                timeout=30,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                print(f"✅ 命令行打印成功")
                return True
            else:
                try:
                    error_msg = result.stderr or '未知错误'
                    print(f"⚠️ 命令行打印失败: {error_msg}")
                except UnicodeDecodeError:
                    print("⚠️ 命令行打印失败: [编码错误，无法显示详细信息]")
                return False

        except Exception as e:
            print(f"⚠️ 命令行打印失败: {e}")
            return False

    def _try_windows_api_print(self, file_path, printer_name):
        """使用Windows API打印"""
        try:
            print("🔄 尝试Windows API打印...")
            import subprocess

            # 使用rundll32调用Windows打印API
            cmd = f'rundll32.exe mshtml.dll,PrintHTML "{file_path}"'
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                print(f"✅ Windows API打印成功")
                return True
            else:
                print(f"⚠️ Windows API打印失败")
                return False

        except Exception as e:
            print(f"⚠️ Windows API打印失败: {e}")
            return False

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            for temp_file in self.temp_files[:]:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self.temp_files.remove(temp_file)
                except:
                    pass
            print("🧹 临时文件已清理")
        except Exception as e:
            print(f"⚠️ 清理临时文件时出错: {e}")

# Excel资源监控器
class ExcelResourceMonitor:
    """Excel资源监控器，确保不影响用户的Excel使用"""

    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.lock = threading.Lock()

    def start_monitoring(self):
        """启动资源监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("📊 Excel资源监控已启动")

    def stop_monitoring(self):
        """停止资源监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("📊 Excel资源监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        import time
        import gc

        while self.monitoring:
            try:
                # 定期强制垃圾回收
                gc.collect()

                # 检查Excel进程数量
                excel_count = self._count_excel_processes()
                if excel_count > 3:  # 如果Excel进程过多
                    print(f"⚠️ 检测到 {excel_count} 个Excel进程，建议清理")

                # 等待下次检查
                time.sleep(5)  # 固定5秒间隔

            except Exception as e:
                print(f"资源监控出错: {e}")
                time.sleep(5)

    def _count_excel_processes(self):
        """统计Excel进程数量"""
        try:
            import subprocess
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq EXCEL.EXE'],
                                  capture_output=True, text=True, timeout=5)
            return result.stdout.count('EXCEL.EXE')
        except:
            return 0

    def force_cleanup_excel_resources(self):
        """强制清理Excel资源"""
        with self.lock:
            try:
                print("🧹 强制清理Excel资源...")

                # 清理无COM打印管理器的临时文件
                no_com_print_manager.cleanup_temp_files()

                # 强制垃圾回收
                import gc
                gc.collect()

                # 清理内存
                try:
                    import ctypes
                    ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)
                except:
                    pass

                print("✅ Excel资源清理完成")

            except Exception as e:
                print(f"⚠️ 强制清理资源时出错: {e}")

# 打印重定向器
class PrintRedirector:
    """重定向print输出到日志系统"""

    def __init__(self, log_func):
        self.log_func = log_func

    def write(self, text):
        if text.strip():
            self.log_func(text.strip())

    def flush(self):
        pass

# 全局无COM打印管理器
no_com_print_manager = NoCOMPrintManager()

# 全局Excel资源监控器（禁用以避免影响用户Excel）
# excel_monitor = ExcelResourceMonitor()  # 已禁用

# 程序退出时的清理函数
def cleanup_on_exit():
    """程序退出时清理所有资源（优化版）"""
    try:
        print("正在清理程序资源...")

        # 停止Excel资源监控（已禁用）
        # try:
        #     excel_monitor.stop_monitoring()
        #     print("✅ Excel资源监控已停止")
        # except:
        #     pass

        # 清理无COM打印管理器
        try:
            no_com_print_manager.cleanup_temp_files()
        except:
            pass

        # 强制清理Excel资源（已禁用以保护用户Excel）
        # try:
        #     excel_monitor.force_cleanup_excel_resources()
        # except:
        #     pass

        # 不再强制关闭Excel进程，保护用户的Excel使用
        # 只清理程序自己创建的资源
        try:
            print("🛡️ 保护用户Excel进程，仅清理程序资源")
            # 这里不做任何Excel进程操作，确保用户Excel不受影响
        except:
            pass

        # 强制垃圾回收
        import gc
        gc.collect()

        # 清理内存
        try:
            import ctypes
            ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)
        except:
            pass

        print("✅ 程序资源清理完成，用户可正常使用Excel")

    except Exception as e:
        print(f"清理资源时出错: {e}")

# 文件锁检查和释放函数
def check_and_release_file_locks():
    """检查并释放可能的文件锁"""
    try:
        import psutil
        import os

        # 获取当前进程
        current_process = psutil.Process()

        # 检查当前进程打开的文件
        open_files = current_process.open_files()
        excel_files = [f for f in open_files if f.path.endswith(('.xlsx', '.xls', '.xlsm'))]

        if excel_files:
            print(f"检测到 {len(excel_files)} 个Excel文件仍被占用")
            for file_info in excel_files:
                print(f"  - {file_info.path}")

        # 强制垃圾回收
        import gc
        gc.collect()

        return len(excel_files)

    except ImportError:
        # 如果没有psutil，使用简单的垃圾回收
        import gc
        gc.collect()
        return 0
    except Exception as e:
        print(f"检查文件锁时出错: {e}")
        return 0

# 注册退出清理函数
import atexit
atexit.register(cleanup_on_exit)

# 工具函数类
class Utils:
    """工具函数集合"""
    
    @staticmethod
    def print_progress_bar(current, total, prefix='', suffix='', length=50, fill='█'):
        """显示进度条"""
        percent = ("{0:.1f}").format(100 * (current / float(total)))
        filled_length = int(length * current // total)
        bar = fill * filled_length + '-' * (length - filled_length)
        print(f'\r{prefix} |{bar}| {percent}% {suffix}', end='', flush=True)
        if current == total:
            print()
    
    @staticmethod
    def determine_product_type(part_number):
        """根据料号前三位判断产品类型"""
        if pd.isna(part_number):
            return ''

        # 确保料号是字符串
        part_number = str(part_number).strip()
        if not part_number:
            return ''

        # 获取前三位字符
        prefix = part_number[:3] if len(part_number) >= 3 else part_number
        prefix = prefix.upper()  # 转换为大写进行匹配

        return Config.PRODUCT_TYPE_MAPPING.get(prefix, '')
    
    @staticmethod
    def get_company_info(company_name):
        """根据公司名称获取完整的公司信息（增强版）"""
        if pd.isna(company_name):
            return None

        company_str = str(company_name).strip()

        # 1. 精确匹配
        for key, info in Config.COMPANY_MAPPING.items():
            if company_str == key or company_str == info['full_name']:
                return info

        # 2. 别名匹配
        for key, info in Config.COMPANY_MAPPING.items():
            if 'aliases' in info:
                for alias in info['aliases']:
                    if alias in company_str or company_str in alias:
                        return info

        # 3. 关键词匹配
        for key, info in Config.COMPANY_MAPPING.items():
            if key in company_str:
                return info

        # 4. 模糊匹配（去除常见后缀）
        clean_company = company_str.replace('有限公司', '').replace('科技', '').replace('集团', '').strip()
        for key, info in Config.COMPANY_MAPPING.items():
            if key in clean_company or clean_company in key:
                return info

        return None

# 性能监控类
class PerformanceMonitor:
    """性能监控和统计"""
    
    def __init__(self):
        self.start_time = time.time()
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'print_success': 0,
            'print_failed': 0,
            'processing_times': [],
            'memory_usage': []
        }
        self.lock = threading.Lock()
    
    def update_stats(self, **kwargs):
        """线程安全的统计更新"""
        with self.lock:
            for key, value in kwargs.items():
                if key in self.stats:
                    if isinstance(self.stats[key], list):
                        self.stats[key].append(value)
                    else:
                        self.stats[key] += value
    
    def get_stats(self):
        """获取当前统计信息"""
        with self.lock:
            elapsed_time = time.time() - self.start_time
            return {
                **self.stats,
                'elapsed_time': elapsed_time,
                'processing_rate': self.stats['total_processed'] / elapsed_time if elapsed_time > 0 else 0
            }
    
    def print_progress(self, current, total):
        """打印进度信息"""
        stats = self.get_stats()
        Utils.print_progress_bar(
            current, 
            total,
            prefix='处理进度',
            suffix=f'(成功:{stats["successful"]} 失败:{stats["failed"]} 跳过:{stats["skipped"]} 速率:{stats["processing_rate"]:.1f}/s)',
            length=40
        )

@contextmanager
def excel_file_manager(template_file):
    """Excel文件上下文管理器，确保文件正确打开和关闭（后台模式）"""
    wb = None
    ws = None
    try:
        print(f"正在后台打开Excel模板文件: {template_file}")

        # 使用openpyxl在后台打开文件，不会显示Excel界面
        # openpyxl是纯Python库，不会启动Excel应用程序
        wb = openpyxl.load_workbook(template_file, read_only=False, keep_vba=False)
        ws = wb.active

        print("Excel模板文件已在后台打开（无界面显示）")
        yield wb, ws

    except Exception as e:
        print(f"打开Excel文件时出错: {str(e)}")
        raise
    finally:
        # 强制关闭工作簿和释放资源
        if wb:
            try:
                print("正在后台关闭Excel文件...")
                wb.close()
                print("Excel文件已在后台关闭")
            except Exception as e:
                print(f"关闭Excel文件时出错: {str(e)}")

        # 强制释放所有引用
        try:
            del wb, ws
        except:
            pass

        # 强制垃圾回收
        import gc
        gc.collect()

        # 额外的内存清理
        try:
            import ctypes
            ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)
        except:
            pass

        print("内存和文件句柄已完全释放")

@contextmanager
def safe_file_operation(file_path):
    """安全文件操作上下文管理器，不使用COM"""
    try:
        print(f"🔧 准备安全操作文件: {os.path.basename(file_path)}")

        # 检查文件是否存在和可访问
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        if not os.access(file_path, os.R_OK):
            raise PermissionError(f"文件无法读取: {file_path}")

        print("✅ 文件访问检查通过")
        yield file_path

    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        raise
    finally:
        # 强制垃圾回收
        import gc
        gc.collect()
        print("🔄 文件操作完成，资源已清理")

class BatchPrintProcessor:
    """批量打印处理器"""

    def __init__(self, current_dir):
        self.current_dir = current_dir
        self.temp_dir = None  # 初始化为None，稍后设置
        self.monitor = PerformanceMonitor()
        self.print_queue = queue.Queue()
        self.window_monitor_active = False
        self.window_monitor_thread = None
        self.setup_silent_mode()
        self.start_window_monitor()

        # 尝试多个位置创建临时目录
        self.initialize_temp_directory()

    def initialize_temp_directory(self):
        """初始化临时目录，尝试多个位置"""
        # 尝试位置1：当前目录下的temp_print
        try:
            temp_dir = os.path.join(self.current_dir, "temp_print")
            print(f"尝试在当前目录创建临时文件夹: {temp_dir}")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            self.temp_dir = temp_dir
            print(f"✓ 成功创建临时目录: {self.temp_dir}")
            return
        except Exception as e:
            print(f"❌ 在当前目录创建临时文件夹失败: {str(e)}")

        # 尝试位置2：用户文档目录
        try:
            docs_dir = os.path.expanduser("~\\Documents")
            temp_dir = os.path.join(docs_dir, "ColorfulShippingTemp")
            print(f"尝试在用户文档目录创建临时文件夹: {temp_dir}")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            self.temp_dir = temp_dir
            print(f"✓ 成功在用户文档目录创建临时目录: {self.temp_dir}")
            return
        except Exception as e:
            print(f"❌ 在用户文档目录创建临时文件夹失败: {str(e)}")

        # 尝试位置3：系统临时目录
        try:
            import tempfile
            temp_dir = os.path.join(tempfile.gettempdir(), "colorful_shipping_temp")
            print(f"尝试在系统临时目录创建文件夹: {temp_dir}")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            self.temp_dir = temp_dir
            print(f"✓ 成功在系统临时目录创建临时文件夹: {self.temp_dir}")
            return
        except Exception as e:
            print(f"❌ 在系统临时目录创建文件夹失败: {str(e)}")

        # 尝试位置4：程序所在目录
        try:
            exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            temp_dir = os.path.join(exe_dir, "temp_print")
            print(f"尝试在程序目录创建临时文件夹: {temp_dir}")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            self.temp_dir = temp_dir
            print(f"✓ 成功在程序目录创建临时文件夹: {self.temp_dir}")
            return
        except Exception as e:
            print(f"❌ 在程序目录创建临时文件夹失败: {str(e)}")

        # 如果所有尝试都失败了
        error_msg = "无法创建临时目录，请检查以下问题：\n"
        error_msg += "1. 是否有足够的磁盘空间\n"
        error_msg += "2. 是否有文件夹创建权限\n"
        error_msg += "3. 是否被杀毒软件阻止\n"
        error_msg += "4. 文件路径是否包含特殊字符"
        print(error_msg)
        raise Exception(error_msg)

    def setup_silent_mode(self):
        """设置系统级别的静默模式，防止任何窗口闪动"""
        try:
            # 设置COM初始化为单线程模式，减少窗口闪动（已禁用）
            # import pythoncom
            # pythoncom.CoInitialize()

            # 尝试设置系统级别的静默选项
            try:
                import win32api
                import win32con
                import win32gui

                # 设置系统参数，减少窗口动画和闪动
                win32api.SystemParametersInfo(win32con.SPI_SETANIMATION, 0, 0, 0)

                # 设置更多系统级别的静默选项
                try:
                    # 禁用窗口动画效果
                    win32api.SystemParametersInfo(win32con.SPI_SETCOMBOBOXANIMATION, 0, False, 0)
                    win32api.SystemParametersInfo(win32con.SPI_SETLISTBOXSMOOTHSCROLLING, 0, False, 0)
                    win32api.SystemParametersInfo(win32con.SPI_SETMENUANIMATION, 0, False, 0)
                    win32api.SystemParametersInfo(win32con.SPI_SETTOOLTIPANIMATION, 0, False, 0)
                except:
                    pass

                # 设置进程级别的窗口隐藏
                try:
                    import os
                    # 设置环境变量，强制Excel以静默模式启动
                    os.environ['EXCEL_SILENT'] = '1'
                    os.environ['OFFICE_SILENT'] = '1'
                    os.environ['AUTOMATION_SILENT'] = '1'
                except:
                    pass

            except:
                pass

            print("🔇 超级静默模式已启用，防止任何窗口闪动")
        except Exception as e:
            print(f"⚠️ 静默模式设置警告: {e}")

    def start_window_monitor(self):
        """启动窗口监控线程，持续隐藏Excel窗口"""
        try:
            self.window_monitor_active = True
            self.window_monitor_thread = threading.Thread(
                target=self.window_monitor_worker,
                daemon=True
            )
            self.window_monitor_thread.start()
            print("🔍 窗口监控线程已启动，将持续隐藏Excel窗口")
        except Exception as e:
            print(f"⚠️ 启动窗口监控失败: {e}")

    def window_monitor_worker(self):
        """窗口监控工作线程"""
        try:
            import win32gui
            import win32con
            import time

            def hide_excel_windows():
                """隐藏所有Excel窗口"""
                try:
                    def enum_windows_callback(hwnd, windows):
                        if win32gui.IsWindowVisible(hwnd):
                            window_text = win32gui.GetWindowText(hwnd)
                            class_name = win32gui.GetClassName(hwnd)
                            if ('Excel' in window_text or 'XLMAIN' in class_name or
                                'Microsoft Excel' in window_text or 'EXCEL' in class_name):
                                win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                                windows.append(hwnd)
                        return True

                    windows = []
                    win32gui.EnumWindows(enum_windows_callback, windows)
                    return len(windows)
                except:
                    return 0

            hidden_count = 0
            while self.window_monitor_active:
                try:
                    count = hide_excel_windows()
                    if count > 0:
                        hidden_count += count
                        if hidden_count % 10 == 0:  # 每隐藏10个窗口报告一次
                            print(f"🔇 已隐藏 {hidden_count} 个Excel窗口")
                    time.sleep(0.1)  # 每100毫秒检查一次
                except Exception as e:
                    if self.window_monitor_active:  # 只在监控活跃时报告错误
                        print(f"⚠️ 窗口监控错误: {e}")
                    time.sleep(1)

        except Exception as e:
            print(f"⚠️ 窗口监控线程错误: {e}")

    def stop_window_monitor(self):
        """停止窗口监控"""
        try:
            self.window_monitor_active = False
            if self.window_monitor_thread and self.window_monitor_thread.is_alive():
                self.window_monitor_thread.join(timeout=2)
            print("🔇 窗口监控已停止")
        except Exception as e:
            print(f"⚠️ 停止窗口监控失败: {e}")

    def wait_for_file_access(self, file_path, timeout=30, operation="访问"):
        """等待文件可以访问"""
        check_interval = 1
        total_checks = timeout // check_interval
        
        for current_check in range(total_checks):
            try:
                with open(file_path, 'r+b'):
                    return True
            except (PermissionError, FileNotFoundError):
                if current_check % 5 == 0:  # 每5秒显示一次进度
                    print(f"等待文件{operation}... ({current_check}s)")
                time.sleep(check_interval)
        
        return False
    
    def check_printer_status(self):
        """检查打印机状态"""
        if not can_print:
            return False, None, "打印功能未启用"
            
        try:
            printer_name = win32print.GetDefaultPrinter()
            if not printer_name:
                return False, None, "未找到默认打印机"
            
            # 获取打印机句柄
            h_printer = win32print.OpenPrinter(printer_name)
            if not h_printer:
                return False, printer_name, "无法连接到打印机"
            
            try:
                # 获取打印机信息
                printer_info = win32print.GetPrinter(h_printer, 2)
                status = printer_info['Status']
                
                # 检查打印机状态
                if status == 0:  # 打印机正常
                    return True, printer_name, None
                else:
                    status_messages = []
                    if status & win32print.PRINTER_STATUS_PAPER_OUT:
                        status_messages.append("缺纸")
                    if status & win32print.PRINTER_STATUS_PAPER_JAM:
                        status_messages.append("卡纸")
                    if status & win32print.PRINTER_STATUS_OFFLINE:
                        status_messages.append("离线")
                    if status & win32print.PRINTER_STATUS_ERROR:
                        status_messages.append("错误")
                    
                    return False, printer_name, "、".join(status_messages) if status_messages else "未知错误"
            finally:
                win32print.ClosePrinter(h_printer)
        except Exception as e:
            return False, None, f"检查打印机状态时出错: {str(e)}"

    def silent_print_excel(self, file_path):
        """完全无COM的Excel打印方法 - 不影响用户Excel/WPS使用"""
        if not can_print:
            print("打印功能未启用，跳过打印")
            return False

        try:
            import os  # 确保os模块可用
            print(f"🖨️ 开始无COM静默打印: {os.path.basename(file_path)}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False

            # 等待文件可访问
            if not self.wait_for_file_access(file_path, timeout=5, operation="打印"):
                print(f"❌ 文件被占用，无法打印: {os.path.basename(file_path)}")
                return False

            # 获取打印机信息
            try:
                import win32print  # 确保在函数内部导入
                printer_name = win32print.GetDefaultPrinter()
                print(f"📍 使用打印机: {printer_name}")
            except Exception as e:
                print(f"❌ 获取打印机失败: {e}")
                printer_name = "默认打印机"

            # 使用无COM打印管理器
            return no_com_print_manager.print_excel_file(file_path, printer_name)

        except Exception as e:
            print(f"❌ 打印过程中出现严重错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def fast_print_excel(self, file_path, printer_name):
        """无COM快速打印Excel文件 - 完全不影响用户Excel/WPS"""
        print(f"🚀 使用无COM打印模式: {os.path.basename(file_path)}")

        # 直接使用无COM打印管理器
        return no_com_print_manager.print_excel_file(file_path, printer_name)





    def try_direct_printer_print(self, file_path, printer_name):
        """使用Windows打印API直接打印（已验证成功的方法）"""
        try:
            print("🔄 尝试Windows打印API直接打印...")
            import subprocess

            # 方法1：使用ShellExecute（已验证成功）
            try:
                import win32api
                result = win32api.ShellExecute(
                    0,
                    "print",
                    file_path,
                    None,
                    os.path.dirname(file_path) or ".",
                    0  # SW_HIDE
                )

                if result > 32:
                    print(f"✅ Windows打印API成功: {os.path.basename(file_path)}")
                    return True
                else:
                    print(f"   ⚠️ ShellExecute返回错误码: {result}")
            except Exception as e:
                print(f"   ⚠️ ShellExecute失败: {e}")

            # 方法2：使用命令行打印
            try:
                cmd = f'print /D:"{printer_name}" "{file_path}"'
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=15,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if result.returncode == 0:
                    print(f"✅ 命令行打印成功: {os.path.basename(file_path)}")
                    return True
                else:
                    print(f"   ⚠️ 命令行打印失败: {result.stderr}")
            except Exception as e:
                print(f"   ⚠️ 命令行打印失败: {e}")

            print(f"   ⚠️ Windows打印API失败")
            return False

        except Exception as e:
            print(f"   ⚠️ Windows打印API失败: {e}")
            return False

    def try_fast_com_print(self, file_path, printer_name):
        """尝试使用COM快速打印 - 最小化资源占用时间"""
        try:
            print("🔄 尝试COM快速打印...")
            import win32com.client
            import pythoncom
            import time
            import gc

            # 初始化COM
            pythoncom.CoInitialize()

            excel_app = None
            workbook = None

            try:
                # 创建独立Excel实例
                excel_app = win32com.client.DispatchEx("Excel.Application")

                # 设置最小化静默模式
                excel_app.Visible = False
                excel_app.DisplayAlerts = False
                excel_app.ScreenUpdating = False
                excel_app.EnableEvents = False
                excel_app.Interactive = False
                excel_app.UserControl = False

                # 快速打开文件（只读模式）
                workbook = excel_app.Workbooks.Open(
                    file_path,
                    UpdateLinks=False,
                    ReadOnly=True,
                    AddToMru=False,
                    Notify=False
                )

                # 立即执行打印
                worksheet = workbook.ActiveSheet
                worksheet.PrintOut(
                    Copies=1,
                    Preview=False,
                    ActivePrinter=printer_name,
                    PrintToFile=False,
                    Collate=True
                )

                print(f"✅ COM快速打印成功: {os.path.basename(file_path)}")
                return True

            finally:
                # 立即强制清理资源
                try:
                    if workbook:
                        workbook.Close(SaveChanges=False)
                except:
                    pass

                try:
                    if excel_app:
                        excel_app.Quit()
                except:
                    pass

                # 强制删除引用
                try:
                    del workbook, excel_app
                except:
                    pass

                # 立即清理COM
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass

                # 强制垃圾回收
                gc.collect()

                # 短暂等待确保资源释放
                time.sleep(0.1)

        except Exception as e:
            print(f"   ⚠️ COM快速打印失败: {e}")
            return False

    def try_powershell_print(self, file_path, printer_name):
        """尝试使用PowerShell快速打印"""
        try:
            print("🔄 尝试PowerShell快速打印...")
            import subprocess

            # 简化的PowerShell脚本，专注于快速打印
            powershell_script = f'''
            $ErrorActionPreference = "SilentlyContinue"
            try {{
                $excel = New-Object -ComObject Excel.Application
                $excel.Visible = $false
                $excel.DisplayAlerts = $false
                $excel.ScreenUpdating = $false

                $workbook = $excel.Workbooks.Open("{file_path}", 0, $true)
                $worksheet = $workbook.ActiveSheet
                $worksheet.PrintOut(1, 1, 1, $false, "{printer_name}")

                $workbook.Close($false)
                $excel.Quit()

                # 立即清理COM对象
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($worksheet) | Out-Null
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
                [System.GC]::Collect()

                Write-Output "SUCCESS"
            }} catch {{
                Write-Output "ERROR: $($_.Exception.Message)"
            }}
            '''

            result = subprocess.run(
                ['powershell.exe', '-WindowStyle', 'Hidden', '-Command', powershell_script],
                capture_output=True,
                text=True,
                timeout=30,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0 and "SUCCESS" in result.stdout:
                print(f"✅ PowerShell快速打印成功: {os.path.basename(file_path)}")
                return True
            else:
                print(f"   ⚠️ PowerShell打印失败: {result.stderr or result.stdout}")
                return False

        except Exception as e:
            print(f"   ⚠️ PowerShell快速打印失败: {e}")
            return False

    def try_system_print(self, file_path):
        """尝试使用系统打印服务（无COM，最快）"""
        try:
            print("🔄 尝试系统打印服务（无COM模式）...")
            import win32api
            import os

            # 使用ShellExecute进行系统级打印，完全不依赖COM
            result = win32api.ShellExecute(
                0,
                "print",
                file_path,
                None,
                os.path.dirname(file_path),
                0  # SW_HIDE
            )

            if result > 32:  # 成功
                print(f"✅ 系统打印成功（无COM）: {os.path.basename(file_path)}")
                return True
            else:
                print(f"   ⚠️ 系统打印失败，错误码: {result}")
                return False

        except Exception as e:
            print(f"   ⚠️ 系统打印失败: {e}")
            return False

    def try_powershell_silent_print(self, file_path, printer_name):
        """尝试使用PowerShell完全静默打印（无窗口闪动）"""
        try:
            print("🔄 尝试PowerShell完全静默打印...")
            import subprocess

            # 极简的PowerShell脚本，避免窗口闪动
            powershell_script = f'''
            $ErrorActionPreference = "SilentlyContinue"
            try {{
                # 设置环境变量强制静默
                $env:EXCEL_SILENT = "1"
                $env:OFFICE_SILENT = "1"

                # 创建Excel对象但立即设置为不可见
                $excel = New-Object -ComObject Excel.Application
                $excel.Visible = $false
                $excel.DisplayAlerts = $false
                $excel.ScreenUpdating = $false
                $excel.EnableEvents = $false
                $excel.Interactive = $false

                # 快速打开、打印、关闭
                $workbook = $excel.Workbooks.Open("{file_path}", 0, $true)
                $workbook.ActiveSheet.PrintOut(1, 1, 1, $false, "{printer_name}")
                $workbook.Close($false)
                $excel.Quit()

                # 立即清理COM对象
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
                [System.GC]::Collect()

                Write-Output "SUCCESS"
            }} catch {{
                Write-Output "ERROR: $($_.Exception.Message)"
            }}
            '''

            result = subprocess.run(
                ['powershell.exe', '-WindowStyle', 'Hidden', '-NoProfile', '-Command', powershell_script],
                capture_output=True,
                text=True,
                timeout=20,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0 and "SUCCESS" in result.stdout:
                print(f"✅ PowerShell静默打印成功: {os.path.basename(file_path)}")
                return True
            else:
                print(f"   ⚠️ PowerShell打印失败: {result.stderr or result.stdout}")
                return False

        except Exception as e:
            print(f"   ⚠️ PowerShell静默打印失败: {e}")
            return False

    def try_file_association_print(self, file_path):
        """尝试使用文件关联打印（无COM）"""
        try:
            print("🔄 尝试文件关联打印...")
            import subprocess
            import os

            # 使用Windows的文件关联进行打印
            cmd = ['cmd', '/c', 'start', '/min', '', '/print', file_path]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                print(f"✅ 文件关联打印成功: {os.path.basename(file_path)}")
                return True
            else:
                print(f"   ⚠️ 文件关联打印失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"   ⚠️ 文件关联打印失败: {e}")
            return False

    def try_minimal_com_print(self, file_path, printer_name):
        """最小化COM打印（作为最后备选）"""
        try:
            print("🔄 尝试最小化COM打印（备选方案）...")
            import win32com.client
            import pythoncom
            import time
            import gc

            # 初始化COM
            pythoncom.CoInitialize()

            excel_app = None
            workbook = None

            try:
                # 创建Excel实例并立即设置为完全不可见
                excel_app = win32com.client.DispatchEx("Excel.Application")

                # 立即设置所有静默属性
                excel_app.Visible = False
                excel_app.DisplayAlerts = False
                excel_app.ScreenUpdating = False
                excel_app.EnableEvents = False
                excel_app.Interactive = False
                excel_app.UserControl = False

                # 尝试隐藏窗口
                try:
                    excel_app.WindowState = -4140  # xlMinimized
                    excel_app.ShowWindowsInTaskbar = False
                except:
                    pass

                # 快速打开文件
                workbook = excel_app.Workbooks.Open(
                    file_path,
                    UpdateLinks=False,
                    ReadOnly=True,
                    AddToMru=False,
                    Notify=False
                )

                # 立即打印
                worksheet = workbook.ActiveSheet
                worksheet.PrintOut(
                    Copies=1,
                    Preview=False,
                    ActivePrinter=printer_name,
                    PrintToFile=False,
                    Collate=True
                )

                print(f"✅ 最小化COM打印成功: {os.path.basename(file_path)}")
                return True

            finally:
                # 立即强制清理
                try:
                    if workbook: workbook.Close(SaveChanges=False)
                    if excel_app: excel_app.Quit()
                except: pass

                try: del workbook, excel_app
                except: pass

                try: pythoncom.CoUninitialize()
                except: pass

                gc.collect()

        except Exception as e:
            print(f"   ⚠️ 最小化COM打印失败: {e}")
            return False

    def process_all_data(self):
        """处理所有数据的主函数（无COM版本）"""
        try:
            print("🚀 开始处理所有数据（无COM版本）...")

            # 读取Excel数据
            df = self.read_excel_data()
            if df is None or df.empty:
                print("❌ 没有数据需要处理")
                return

            print(f"📊 共读取到 {len(df)} 条数据")

            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    print(f"\n📝 处理第 {index + 1} 条数据...")

                    # 生成托运单文件
                    temp_file = self.generate_shipping_document(row, index + 1)
                    if temp_file:
                        print(f"✅ 第 {index + 1} 条数据处理完成")

                        # 打印文件
                        if can_print:
                            self.silent_print_excel(temp_file)
                    else:
                        print(f"❌ 第 {index + 1} 条数据处理失败")

                except Exception as e:
                    print(f"❌ 处理第 {index + 1} 条数据时出错: {e}")
                    continue

            print("\n🎉 所有数据处理完成！")

        except Exception as e:
            print(f"❌ 处理数据时出现严重错误: {e}")
            import traceback
            traceback.print_exc()

    def fill_excel_data(self, ws, row_data, blank_template):
        """优化的Excel数据填写方法 - 处理合并单元格"""
        try:
            # 重置为空白状态
            for cell_ref, value in blank_template.items():
                try:
                    # 检查是否是合并单元格
                    cell = ws[cell_ref]
                    if hasattr(cell, 'coordinate') and cell.coordinate in ws.merged_cells:
                        # 如果是合并单元格，需要特殊处理
                        merged_range = None
                        for merged_cell in ws.merged_cells.ranges:
                            if cell.coordinate in merged_cell:
                                merged_range = merged_cell
                                break

                        if merged_range:
                            # 取消合并，设置值，然后重新合并
                            ws.unmerge_cells(str(merged_range))
                            ws[cell_ref] = value
                            ws.merge_cells(str(merged_range))
                        else:
                            ws[cell_ref] = value
                    else:
                        ws[cell_ref] = value
                except Exception as e:
                    print(f"⚠️ 重置单元格 {cell_ref} 失败: {e}")
                    # 尝试直接设置值
                    try:
                        ws[cell_ref] = value
                    except:
                        pass

            # 填写新数据
            for mapping in Config.CELL_MAPPING_RULES:
                data_col = mapping['data_col']
                if data_col in row_data and pd.notna(row_data[data_col]):
                    cell_ref = f"{mapping['excel_col']}{mapping['excel_row']}"
                    new_value = row_data[data_col]

                    try:
                        # 检查是否是合并单元格
                        cell = ws[cell_ref]
                        if hasattr(cell, 'coordinate'):
                            # 查找包含此单元格的合并区域
                            merged_range = None
                            for merged_cell in ws.merged_cells.ranges:
                                if cell.coordinate in merged_cell:
                                    merged_range = merged_cell
                                    break

                            if merged_range:
                                print(f"📝 处理合并单元格 {cell_ref} (范围: {merged_range})")
                                # 取消合并，设置值，然后重新合并
                                ws.unmerge_cells(str(merged_range))
                                ws[cell_ref] = new_value
                                ws.merge_cells(str(merged_range))
                                print(f"✅ 合并单元格 {cell_ref} 设置成功: {new_value}")
                            else:
                                # 普通单元格
                                ws[cell_ref] = new_value
                                print(f"✅ 普通单元格 {cell_ref} 设置成功: {new_value}")
                        else:
                            ws[cell_ref] = new_value
                            print(f"✅ 单元格 {cell_ref} 设置成功: {new_value}")

                    except Exception as e:
                        print(f"⚠️ 设置单元格 {cell_ref} 失败: {e}")
                        # 尝试强制设置
                        try:
                            # 如果是合并单元格错误，尝试直接设置到左上角单元格
                            if "MergedCell" in str(e):
                                # 获取合并区域的左上角单元格
                                for merged_cell in ws.merged_cells.ranges:
                                    if cell_ref in str(merged_cell):
                                        top_left = merged_cell.start_cell
                                        ws.unmerge_cells(str(merged_cell))
                                        ws[top_left.coordinate] = new_value
                                        ws.merge_cells(str(merged_cell))
                                        print(f"✅ 强制设置合并单元格 {cell_ref} 成功: {new_value}")
                                        break
                            else:
                                ws[cell_ref] = new_value
                                print(f"✅ 强制设置单元格 {cell_ref} 成功: {new_value}")
                        except Exception as e2:
                            print(f"❌ 强制设置单元格 {cell_ref} 也失败: {e2}")

            # 最后确保所有填写的单元格格式一致性
            self.ensure_all_cells_formatting_consistency(ws)

            return True

        except Exception as e:
            print(f"❌ 填写Excel数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def ensure_all_cells_formatting_consistency(self, ws):
        """确保所有单元格格式与模板保持一致，防止打印时字体位置偏移"""
        try:
            print("  🔧 确保单元格格式一致性...")

            # 检查所有映射的单元格
            for mapping in Config.CELL_MAPPING_RULES:
                cell_ref = f"{mapping['excel_col']}{mapping['excel_row']}"
                try:
                    cell = ws[cell_ref]

                    # 确保字体设置完整
                    if cell.font:
                        from openpyxl.styles import Font
                        # 重新设置字体，确保所有属性都正确
                        cell.font = Font(
                            name=cell.font.name or "宋体",  # 确保有默认字体
                            size=cell.font.size or 11,     # 确保有默认大小
                            bold=cell.font.bold,
                            italic=cell.font.italic,
                            vertAlign=cell.font.vertAlign,
                            underline=cell.font.underline,
                            strike=cell.font.strike,
                            color=cell.font.color
                        )

                    # 确保对齐设置完整
                    if cell.alignment:
                        from openpyxl.styles import Alignment
                        # 重新设置对齐，确保所有属性都正确
                        cell.alignment = Alignment(
                            horizontal=cell.alignment.horizontal or "general",
                            vertical=cell.alignment.vertical or "bottom",
                            text_rotation=cell.alignment.text_rotation or 0,
                            wrap_text=cell.alignment.wrap_text,
                            shrink_to_fit=cell.alignment.shrink_to_fit,
                            indent=cell.alignment.indent or 0
                        )

                except Exception as e:
                    print(f"   ⚠️ 格式一致性检查失败 {cell_ref}: {e}")

            print("  ✅ 格式一致性检查完成")

        except Exception as e:
            print(f"   ⚠️ 整体格式一致性检查失败: {e}")

    def apply_precision_print_settings(self, ws, source_ws):
        """应用精确的打印设置，确保字体位置完全一致"""
        try:
            print("  🎯 应用精确打印设置...")

            if not Config.PRINT_PRECISION_CONFIG.get('ensure_exact_scaling', True):
                return

            # 1. 强制使用模板的精确页边距
            if Config.PRINT_PRECISION_CONFIG.get('force_template_margins', True):
                try:
                    ws.page_margins.left = source_ws.page_margins.left
                    ws.page_margins.right = source_ws.page_margins.right
                    ws.page_margins.top = source_ws.page_margins.top
                    ws.page_margins.bottom = source_ws.page_margins.bottom
                    ws.page_margins.header = source_ws.page_margins.header
                    ws.page_margins.footer = source_ws.page_margins.footer
                    print(f"   ✓ 强制应用模板页边距")
                except Exception as e:
                    print(f"   ⚠️ 页边距设置失败: {e}")

            # 2. 确保精确的缩放设置
            if Config.PRINT_PRECISION_CONFIG.get('ensure_exact_scaling', True):
                try:
                    # 强制使用模板的缩放设置
                    ws.page_setup.scale = source_ws.page_setup.scale
                    ws.page_setup.fitToWidth = source_ws.page_setup.fitToWidth
                    ws.page_setup.fitToHeight = source_ws.page_setup.fitToHeight

                    # 禁用自动适应（关键）
                    if Config.PRINT_PRECISION_CONFIG.get('disable_auto_fit', True):
                        ws.page_setup.fitToWidth = None
                        ws.page_setup.fitToHeight = None
                        # 使用固定缩放比例
                        ws.page_setup.scale = source_ws.page_setup.scale or 100

                    print(f"   ✓ 精确缩放设置: {ws.page_setup.scale}%")
                except Exception as e:
                    print(f"   ⚠️ 缩放设置失败: {e}")

            # 3. 锁定打印区域
            if Config.PRINT_PRECISION_CONFIG.get('lock_print_area', True):
                try:
                    if hasattr(source_ws, 'print_area') and source_ws.print_area:
                        ws.print_area = source_ws.print_area
                        print(f"   ✓ 锁定打印区域: {source_ws.print_area}")
                    else:
                        # 清除打印区域，使用整个工作表
                        ws.print_area = None
                        print("   ✓ 使用整个工作表作为打印区域")
                except Exception as e:
                    print(f"   ⚠️ 打印区域设置失败: {e}")

            # 4. 精确DPI设置
            if Config.PRINT_PRECISION_CONFIG.get('use_exact_dpi', True):
                try:
                    if hasattr(source_ws.page_setup, 'horizontalDpi'):
                        ws.page_setup.horizontalDpi = source_ws.page_setup.horizontalDpi
                    if hasattr(source_ws.page_setup, 'verticalDpi'):
                        ws.page_setup.verticalDpi = source_ws.page_setup.verticalDpi
                    print("   ✓ 精确DPI设置已应用")
                except Exception as e:
                    print(f"   ⚠️ DPI设置失败: {e}")

            # 5. 确保打印选项完全一致
            try:
                ws.print_options.horizontalCentered = source_ws.print_options.horizontalCentered
                ws.print_options.verticalCentered = source_ws.print_options.verticalCentered

                # 确保网格线和标题设置一致
                if hasattr(source_ws.print_options, 'gridLines'):
                    ws.print_options.gridLines = source_ws.print_options.gridLines
                if hasattr(source_ws.print_options, 'headings'):
                    ws.print_options.headings = source_ws.print_options.headings

                print("   ✓ 打印选项完全同步")
            except Exception as e:
                print(f"   ⚠️ 打印选项设置失败: {e}")

            print("  ✅ 精确打印设置完成")

        except Exception as e:
            print(f"   ⚠️ 精确打印设置失败: {e}")

    def copy_workbook_with_formatting(self, source_wb):
        """完整复制工作簿及其所有格式"""
        try:

            import openpyxl
            from openpyxl.styles import Font, Alignment, Border, PatternFill, Side
            from openpyxl.utils import get_column_letter

            # 创建新的工作簿
            new_wb = openpyxl.Workbook()
            new_ws = new_wb.active
            source_ws = source_wb.active

            # 设置工作表名称
            new_ws.title = source_ws.title

            print(f"📋 开始复制工作簿格式...")

            # 1. 复制行高
            print("  📏 复制行高...")
            for row_num in range(1, source_ws.max_row + 1):
                if source_ws.row_dimensions[row_num].height:
                    new_ws.row_dimensions[row_num].height = source_ws.row_dimensions[row_num].height

            # 2. 复制列宽
            print("  📐 复制列宽...")
            for col_num in range(1, source_ws.max_column + 1):
                col_letter = get_column_letter(col_num)
                if source_ws.column_dimensions[col_letter].width:
                    new_ws.column_dimensions[col_letter].width = source_ws.column_dimensions[col_letter].width

            # 3. 复制合并单元格
            print("  🔗 复制合并单元格...")
            for merged_range in source_ws.merged_cells.ranges:
                new_ws.merge_cells(str(merged_range))

            # 4. 复制所有单元格的值和格式
            print("  🎨 复制单元格格式和内容...")
            for row in range(1, source_ws.max_row + 1):
                for col in range(1, source_ws.max_column + 1):
                    source_cell = source_ws.cell(row=row, column=col)
                    new_cell = new_ws.cell(row=row, column=col)

                    # 检查是否是合并单元格
                    from openpyxl.cell.cell import MergedCell
                    if isinstance(source_cell, MergedCell):
                        # 跳过合并单元格的值复制，只复制格式
                        pass
                    else:
                        # 复制值
                        new_cell.value = source_cell.value

                    # 只为非合并单元格复制格式
                    if not isinstance(source_cell, MergedCell):
                        # 复制字体
                        if source_cell.font:
                            new_cell.font = Font(
                                name=source_cell.font.name,
                                size=source_cell.font.size,
                                bold=source_cell.font.bold,
                                italic=source_cell.font.italic,
                                vertAlign=source_cell.font.vertAlign,
                                underline=source_cell.font.underline,
                                strike=source_cell.font.strike,
                                color=source_cell.font.color
                            )

                        # 复制对齐
                        if source_cell.alignment:
                            new_cell.alignment = Alignment(
                                horizontal=source_cell.alignment.horizontal,
                                vertical=source_cell.alignment.vertical,
                                text_rotation=source_cell.alignment.text_rotation,
                                wrap_text=source_cell.alignment.wrap_text,
                                shrink_to_fit=source_cell.alignment.shrink_to_fit,
                                indent=source_cell.alignment.indent
                            )

                        # 复制边框
                        if source_cell.border:
                            new_cell.border = Border(
                                left=Side(border_style=source_cell.border.left.style, color=source_cell.border.left.color),
                                right=Side(border_style=source_cell.border.right.style, color=source_cell.border.right.color),
                                top=Side(border_style=source_cell.border.top.style, color=source_cell.border.top.color),
                                bottom=Side(border_style=source_cell.border.bottom.style, color=source_cell.border.bottom.color)
                            )

                        # 复制填充
                        if source_cell.fill:
                            new_cell.fill = PatternFill(
                                fill_type=source_cell.fill.fill_type,
                                start_color=source_cell.fill.start_color,
                                end_color=source_cell.fill.end_color
                            )

                        # 复制数字格式（重要：包括日期格式）
                        if source_cell.number_format:
                            new_cell.number_format = source_cell.number_format

            # 5. 复制页面设置（增强版）
            print("  📄 精确复制页面设置...")
            try:
                new_ws.page_setup.orientation = source_ws.page_setup.orientation
                new_ws.page_setup.paperSize = source_ws.page_setup.paperSize

                # 精确复制页边距
                new_ws.page_margins.left = source_ws.page_margins.left
                new_ws.page_margins.right = source_ws.page_margins.right
                new_ws.page_margins.top = source_ws.page_margins.top
                new_ws.page_margins.bottom = source_ws.page_margins.bottom
                new_ws.page_margins.header = source_ws.page_margins.header
                new_ws.page_margins.footer = source_ws.page_margins.footer

                print(f"   ✓ 页边距 - 左:{source_ws.page_margins.left}, 右:{source_ws.page_margins.right}")
                print(f"   ✓ 页边距 - 上:{source_ws.page_margins.top}, 下:{source_ws.page_margins.bottom}")
            except Exception as margin_error:
                print(f"   ⚠️ 页边距设置失败: {margin_error}")

            # 6. 复制打印缩放设置（关键：影响字体位置）
            print("  🔍 精确复制打印缩放设置...")
            try:
                # 复制缩放设置
                new_ws.page_setup.scale = source_ws.page_setup.scale
                new_ws.page_setup.fitToWidth = source_ws.page_setup.fitToWidth
                new_ws.page_setup.fitToHeight = source_ws.page_setup.fitToHeight

                # 复制打印质量和DPI设置
                if hasattr(source_ws.page_setup, 'horizontalDpi'):
                    new_ws.page_setup.horizontalDpi = source_ws.page_setup.horizontalDpi
                if hasattr(source_ws.page_setup, 'verticalDpi'):
                    new_ws.page_setup.verticalDpi = source_ws.page_setup.verticalDpi

                print(f"   ✓ 缩放比例: {source_ws.page_setup.scale}%")
                print(f"   ✓ 适合页宽: {source_ws.page_setup.fitToWidth}")
                print(f"   ✓ 适合页高: {source_ws.page_setup.fitToHeight}")
            except Exception as scale_error:
                print(f"   ⚠️ 缩放设置复制失败: {scale_error}")

            # 7. 复制打印选项（增强版）
            print("  🖨️ 精确复制打印选项...")
            try:
                new_ws.print_options.horizontalCentered = source_ws.print_options.horizontalCentered
                new_ws.print_options.verticalCentered = source_ws.print_options.verticalCentered

                # 复制更多打印选项
                if hasattr(source_ws.print_options, 'headings'):
                    new_ws.print_options.headings = source_ws.print_options.headings
                if hasattr(source_ws.print_options, 'gridLines'):
                    new_ws.print_options.gridLines = source_ws.print_options.gridLines
                if hasattr(source_ws.print_options, 'gridLinesSet'):
                    new_ws.print_options.gridLinesSet = source_ws.print_options.gridLinesSet

                print(f"   ✓ 水平居中: {source_ws.print_options.horizontalCentered}")
                print(f"   ✓ 垂直居中: {source_ws.print_options.verticalCentered}")
            except Exception as print_opt_error:
                print(f"   ⚠️ 打印选项复制失败: {print_opt_error}")

            # 8. 复制打印区域设置
            print("  📐 复制打印区域设置...")
            try:
                if hasattr(source_ws, 'print_area') and source_ws.print_area:
                    new_ws.print_area = source_ws.print_area
                    print(f"   ✓ 打印区域: {source_ws.print_area}")
                else:
                    print("   ✓ 无特定打印区域（打印整个工作表）")
            except Exception as area_error:
                print(f"   ⚠️ 打印区域复制失败: {area_error}")

            # 9. 复制页面布局设置
            print("  📋 复制页面布局设置...")
            try:
                # 复制页面顺序
                if hasattr(source_ws.page_setup, 'pageOrder'):
                    new_ws.page_setup.pageOrder = source_ws.page_setup.pageOrder

                # 复制打印标题
                if hasattr(source_ws, 'print_title_rows') and source_ws.print_title_rows:
                    new_ws.print_title_rows = source_ws.print_title_rows
                    print(f"   ✓ 打印标题行: {source_ws.print_title_rows}")
                if hasattr(source_ws, 'print_title_cols') and source_ws.print_title_cols:
                    new_ws.print_title_cols = source_ws.print_title_cols
                    print(f"   ✓ 打印标题列: {source_ws.print_title_cols}")

            except Exception as layout_error:
                print(f"   ⚠️ 页面布局设置复制失败: {layout_error}")

            # 10. 应用精确打印设置（新增）
            self.apply_precision_print_settings(new_ws, source_ws)

            print("  ✅ 工作簿格式复制完成（增强版）")
            return new_wb

        except Exception as e:
            print(f"❌ 复制工作簿格式时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def create_temp_file(self, template_wb, row_index, timestamp, row_data=None):
        """创建临时打印文件（使用单身备注内容命名）- 完整保持模板格式"""
        try:
            # 获取当前日期
            from datetime import datetime
            current_date = datetime.now().strftime("%Y%m%d")

            # 尝试从单身备注中提取有意义的文件名
            if row_data is not None and '单身备注' in row_data:
                remark = str(row_data['单身备注']).strip()
                if remark and remark != 'nan':
                    # 提取单身备注中的关键信息作为文件名
                    filename_base = self.extract_filename_from_remark(remark)
                    if filename_base:
                        temp_filename = f"{filename_base}_{current_date}_{row_index:03d}.xlsx"
                    else:
                        temp_filename = f"托运单_{row_index:03d}_{current_date}_{timestamp}.xlsx"
                else:
                    temp_filename = f"托运单_{row_index:03d}_{current_date}_{timestamp}.xlsx"
            else:
                temp_filename = f"托运单_{row_index:03d}_{current_date}_{timestamp}.xlsx"

            # 创建完整格式副本
            print(f"📄 为第 {row_index} 条数据创建格式化临时文件...")
            new_wb = self.copy_workbook_with_formatting(template_wb)
            if not new_wb:
                print(f"❌ 无法创建格式化副本")
                return None

            temp_file_path = os.path.join(self.temp_dir, temp_filename)
            new_wb.save(temp_file_path)
            new_wb.close()  # 关闭新创建的工作簿

            print(f"✅ 临时文件已创建: {temp_filename}")
            return temp_file_path

        except Exception as e:
            print(f"❌ 创建临时文件时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def extract_filename_from_remark(self, remark):
        """从单身备注中提取适合作为文件名的内容（使用完整备注内容）"""
        try:
            import re

            # 清理备注内容
            remark = str(remark).strip()
            if not remark or remark == 'nan':
                return None

            # 移除或替换不适合文件名的字符
            # Windows文件名不能包含: \ / : * ? " < > |
            clean_remark = remark

            # 替换不允许的字符为下划线或空格
            forbidden_chars = r'[\\/:*?"<>|]'
            clean_remark = re.sub(forbidden_chars, '_', clean_remark)

            # 替换多个连续的空格为单个空格
            clean_remark = re.sub(r'\s+', ' ', clean_remark)

            # 替换中文标点符号
            clean_remark = clean_remark.replace('，', '_')
            clean_remark = clean_remark.replace('。', '_')
            clean_remark = clean_remark.replace('、', '_')
            clean_remark = clean_remark.replace('；', '_')
            clean_remark = clean_remark.replace('：', '_')
            clean_remark = clean_remark.replace('（', '(')
            clean_remark = clean_remark.replace('）', ')')
            clean_remark = clean_remark.replace('【', '[')
            clean_remark = clean_remark.replace('】', ']')

            # 移除首尾空格和下划线
            clean_remark = clean_remark.strip(' _')

            # 限制文件名长度（Windows文件名限制为255字符，但为了实用性，我们限制为100字符）
            max_length = 80  # 为日期和序号留出空间
            if len(clean_remark) > max_length:
                # 尝试在合适的位置截断（如空格、下划线处）
                truncate_pos = max_length
                for i in range(max_length - 10, max_length):
                    if i < len(clean_remark) and clean_remark[i] in [' ', '_', '-']:
                        truncate_pos = i
                        break
                clean_remark = clean_remark[:truncate_pos].rstrip(' _-')

            # 确保文件名不为空
            if not clean_remark:
                return None

            return f"托运单_{clean_remark}"

        except Exception as e:
            print(f"⚠️ 提取文件名失败: {e}")
            return None

    def print_worker(self, print_queue, results_queue):
        """打印工作线程（后台打印，不启动Excel）"""
        while True:
            try:
                item = print_queue.get(timeout=1)
                if item is None:  # 结束信号
                    break

                file_path, row_index, _ = item

                # 使用后台打印方法，不启动Excel应用程序
                try:
                    success = self.silent_print_excel(file_path)
                    if success:
                        results_queue.put(('success', row_index, file_path))
                    else:
                        results_queue.put(('failed', row_index, "后台打印失败"))
                    time.sleep(Config.PRINT_DELAY)  # 避免打印机缓冲区溢出
                except Exception as e:
                    results_queue.put(('failed', row_index, str(e)))

                print_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"打印工作线程出错: {str(e)}")
                break

    def process_batch_data(self, df_batch, template_wb, blank_template, batch_start_index, timestamp):
        """处理一批数据 - 为每条数据创建独立的格式化临时文件"""
        temp_files = []

        for i, (_, row) in enumerate(df_batch.iterrows()):
            current_row = batch_start_index + i + 1

            try:
                print(f"📝 处理第 {current_row} 条数据...")

                # 1. 创建模板的完整格式副本
                temp_file = self.create_temp_file(template_wb, current_row, timestamp, row)
                if not temp_file:
                    print(f"❌ 第 {current_row} 条数据：创建临时文件失败")
                    self.monitor.update_stats(failed=1, total_processed=1)
                    continue

                # 2. 打开刚创建的临时文件并填写数据
                temp_wb = None
                temp_ws = None
                try:
                    import openpyxl
                    temp_wb = openpyxl.load_workbook(temp_file, read_only=False)
                    temp_ws = temp_wb.active

                    # 填写数据到临时文件
                    if self.fill_excel_data(temp_ws, row, blank_template):
                        # 保存填写了数据的临时文件
                        temp_wb.save(temp_file)

                        # 立即关闭文件
                        temp_wb.close()
                        temp_wb = None
                        temp_ws = None

                        temp_files.append((temp_file, current_row))
                        self.monitor.update_stats(successful=1)
                        print(f"✅ 第 {current_row} 条数据处理成功")
                    else:
                        # 关闭文件
                        temp_wb.close()
                        temp_wb = None
                        temp_ws = None

                        # 删除失败的临时文件
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                        print(f"⚠️ 第 {current_row} 条数据：填写数据失败")
                        self.monitor.update_stats(skipped=1)

                except Exception as temp_e:
                    print(f"❌ 第 {current_row} 条数据：处理临时文件时出错: {temp_e}")
                    # 强制清理临时文件和资源
                    try:
                        if temp_wb:
                            temp_wb.close()
                    except:
                        pass

                    try:
                        del temp_wb, temp_ws
                    except:
                        pass

                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except:
                        pass
                    self.monitor.update_stats(failed=1)

                finally:
                    # 确保资源被释放
                    try:
                        if temp_wb:
                            temp_wb.close()
                        del temp_wb, temp_ws
                    except:
                        pass

                    # 强制垃圾回收
                    import gc
                    gc.collect()

                self.monitor.update_stats(total_processed=1)

            except Exception as e:
                print(f"❌ 处理第 {current_row} 条数据时出错: {str(e)}")
                self.monitor.update_stats(failed=1, total_processed=1)

        return temp_files

    def optimized_batch_print(self, df, template_file):
        """优化的批量打印处理"""
        print("开始优化批量打印处理...")
        print(f"数据总量: {len(df)} 条")
        print(f"批处理大小: {Config.BATCH_SIZE}")
        print(f"最大线程数: {Config.MAX_WORKERS}")

        total_rows = len(df)
        timestamp = int(time.time())
        all_temp_files = []

        # 检查打印机状态
        printer_ok, printer_name, error_msg = self.check_printer_status()
        if not printer_ok:
            print(f"打印机错误: {error_msg}")
            return

        print(f"使用打印机: {printer_name}")

        try:
            # 使用上下文管理器打开Excel文件
            with excel_file_manager(template_file) as (wb, ws):

                # 保存原始模板状态
                print("保存模板原始状态...")
                blank_template = {}
                for mapping in Config.CELL_MAPPING_RULES:
                    if mapping['excel_col'] and mapping['excel_row']:
                        cell_ref = f"{mapping['excel_col']}{mapping['excel_row']}"
                        blank_template[cell_ref] = ws[cell_ref].value
                print("原始状态保存完成")

                # 第一阶段：按安捷通托运书.xlsx模板生成所有文件
                print(f"\n{'='*60}")
                print("第一阶段：按安捷通托运书.xlsx模板生成所有文件...")
                print(f"{'='*60}")

                for batch_start in range(0, total_rows, Config.BATCH_SIZE):
                    batch_end = min(batch_start + Config.BATCH_SIZE, total_rows)
                    df_batch = df.iloc[batch_start:batch_end]

                    print(f"\n生成批次 {batch_start//Config.BATCH_SIZE + 1}: 第 {batch_start+1}-{batch_end} 条数据")

                    # 处理当前批次
                    batch_temp_files = self.process_batch_data(
                        df_batch, wb, blank_template, batch_start, timestamp
                    )
                    all_temp_files.extend(batch_temp_files)

                    # 显示进度
                    self.monitor.print_progress(batch_end, total_rows)

                    # 定期清理内存
                    if batch_start % (Config.BATCH_SIZE * 5) == 0:
                        gc.collect()

                print(f"\n✅ 文件生成完成！共生成了 {len(all_temp_files)} 个托运单文件")

                # 第二阶段：逐个打印所有生成的文件
                if all_temp_files:
                    print(f"\n{'='*60}")
                    print("第二阶段：开始逐个打印所有生成的文件...")
                    print(f"{'='*60}")
                    self.sequential_print_all_files(all_temp_files)

                # 恢复模板原始状态
                print("\n恢复模板到原始状态...")
                for cell_ref, value in blank_template.items():
                    ws[cell_ref] = value
                print("模板已恢复到原始状态")

        except Exception as e:
            print(f"批量打印过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

        finally:
            # 停止窗口监控
            self.stop_window_monitor()

            # 保留临时文件（不删除）
            self.preserve_temp_files(all_temp_files)

            # 清理无COM打印管理器的临时文件
            no_com_print_manager.cleanup_temp_files()

            # 检查并释放文件锁
            locked_files = check_and_release_file_locks()
            if locked_files > 0:
                print(f"⚠️ 仍有 {locked_files} 个文件被锁定，程序退出后会自动释放")

            # 显示最终统计
            self.print_final_stats()

    def sequential_print_all_files(self, temp_files):
        """逐个打印所有生成的文件（新的打印方法）"""
        print(f"准备逐个打印 {len(temp_files)} 个文件...")
        print("打印方式：按顺序逐个打印，确保打印质量")

        print_success = 0
        print_failed = 0

        for i, (temp_file, row_index) in enumerate(temp_files, 1):
            try:
                print(f"\n🖨️ 正在打印第 {i}/{len(temp_files)} 个文件...")
                print(f"   文件: {os.path.basename(temp_file)}")
                print(f"   数据行: 第 {row_index} 条")

                # 等待文件可访问
                if not self.wait_for_file_access(temp_file, timeout=10, operation="打印"):
                    print(f"   ❌ 文件被占用，跳过打印")
                    print_failed += 1
                    continue

                # 执行打印
                success = self.silent_print_excel(temp_file)

                if success:
                    print_success += 1
                    print(f"   ✅ 打印成功 ({print_success}/{len(temp_files)})")
                else:
                    print_failed += 1
                    print(f"   ❌ 打印失败 ({print_failed}/{len(temp_files)})")

                # 显示总体进度
                Utils.print_progress_bar(
                    i,
                    len(temp_files),
                    prefix='总体打印进度',
                    suffix=f'(成功:{print_success} 失败:{print_failed})',
                    length=40
                )

                # 缩短打印间隔，快速释放资源
                if i < len(temp_files):  # 不是最后一个文件
                    print(f"   ⏳ 等待 0.5 秒后继续下一个...")
                    time.sleep(0.5)  # 缩短等待时间，快速释放资源

            except Exception as e:
                print_failed += 1
                print(f"   ❌ 打印第 {i} 个文件时出错: {str(e)}")

        # 更新统计
        self.monitor.update_stats(print_success=print_success, print_failed=print_failed)

        print(f"\n{'='*60}")
        print("逐个打印完成！")
        print(f"{'='*60}")
        print(f"总文件数: {len(temp_files)}")
        print(f"打印成功: {print_success}")
        print(f"打印失败: {print_failed}")
        print(f"成功率: {(print_success/len(temp_files)*100):.1f}%" if temp_files else "0%")

        if print_success > 0:
            print(f"\n✅ 已成功发送 {print_success} 个打印任务到打印机")
            print("请检查打印机输出，确认所有文件都已正确打印")

    def batch_send_print_jobs(self, temp_files, total_rows):
        """批量发送打印任务（多线程）"""
        print(f"\n{'='*60}")
        print(f"开始批量发送 {len(temp_files)} 个打印任务")
        print(f"{'='*60}")

        # 创建打印队列和结果队列
        print_queue = queue.Queue()
        results_queue = queue.Queue()

        # 启动打印工作线程
        print_threads = []
        for i in range(min(Config.MAX_WORKERS, len(temp_files))):
            thread = threading.Thread(
                target=self.print_worker,
                args=(print_queue, results_queue),
                daemon=True
            )
            thread.start()
            print_threads.append(thread)

        # 将打印任务加入队列
        for temp_file, row_index in temp_files:
            print_queue.put((temp_file, row_index, total_rows))

        # 监控打印进度
        completed = 0
        print_success = 0
        print_failed = 0

        while completed < len(temp_files):
            try:
                result = results_queue.get(timeout=1)
                status, row_index, info = result

                completed += 1
                if status == 'success':
                    print_success += 1
                    print(f"✓ 第 {row_index} 条打印任务发送成功 ({completed}/{len(temp_files)})")
                else:
                    print_failed += 1
                    print(f"✗ 第 {row_index} 条打印任务失败: {info} ({completed}/{len(temp_files)})")

                # 更新进度条
                Utils.print_progress_bar(
                    completed,
                    len(temp_files),
                    prefix='打印进度',
                    suffix=f'(成功:{print_success} 失败:{print_failed})',
                    length=40
                )

            except queue.Empty:
                continue

        # 停止工作线程
        for _ in print_threads:
            print_queue.put(None)

        for thread in print_threads:
            thread.join(timeout=5)

        # 更新统计
        self.monitor.update_stats(print_success=print_success, print_failed=print_failed)

        print(f"\n打印任务发送完成！")
        print(f"成功: {print_success} 个")
        print(f"失败: {print_failed} 个")

        # 智能等待打印完成
        if print_success > 0:
            self.wait_for_print_completion_smart(print_success, temp_files)

    def wait_for_print_completion_smart(self, print_success_count, temp_files):
        """智能等待打印完成"""
        print(f"\n{'='*60}")
        print("智能等待打印完成...")
        print(f"{'='*60}")

        # 计算预估打印时间
        estimated_time_per_page = 3  # 每页预估3秒
        base_wait_time = print_success_count * estimated_time_per_page
        max_wait_time = max(30, min(base_wait_time, 300))  # 最少30秒，最多5分钟

        print(f"预估打印时间: {base_wait_time} 秒")
        print(f"实际等待时间: {max_wait_time} 秒")
        print("正在监控打印进度...")

        # 分阶段等待，每10秒检查一次
        check_interval = 10
        total_checks = max_wait_time // check_interval

        for check in range(total_checks):
            current_time = (check + 1) * check_interval
            remaining_time = max_wait_time - current_time

            print(f"  等待进度: {current_time}/{max_wait_time}秒 (剩余: {remaining_time}秒)")

            # 检查文件是否仍被占用
            locked_count = self.check_temp_files_status(temp_files)
            if locked_count == 0:
                print(f"  ✓ 所有文件已释放，打印可能已完成")
                break
            else:
                print(f"  ⏳ 还有 {locked_count} 个文件被占用，继续等待...")

            time.sleep(check_interval)

        print(f"\n智能等待完成！")
        print("开始清理临时文件...")

    def check_temp_files_status(self, temp_files):
        """检查临时文件状态"""
        locked_count = 0

        for temp_file, _ in temp_files:
            if os.path.exists(temp_file):
                try:
                    # 尝试以独占模式打开文件
                    with open(temp_file, 'r+b') as f:
                        pass  # 如果能打开，说明没有被锁定
                except (PermissionError, IOError):
                    locked_count += 1

        return locked_count

    def preserve_temp_files(self, temp_files):
        """保留临时文件（不删除，便于用户查看和管理）"""
        print(f"\n{'='*60}")
        print("保留临时文件...")
        print(f"{'='*60}")

        if not temp_files:
            print("没有临时文件需要处理")
            return

        # 统计文件信息
        existing_files = []
        missing_files = []

        for temp_file, row_index in temp_files:
            if os.path.exists(temp_file):
                existing_files.append((temp_file, row_index))
            else:
                missing_files.append((temp_file, row_index))

        print(f"📁 临时文件保留统计:")
        print(f"  总文件数: {len(temp_files)}")
        print(f"  成功保留: {len(existing_files)}")
        print(f"  文件丢失: {len(missing_files)}")

        if existing_files:
            print(f"\n✅ 已保留的临时文件:")
            print(f"📂 保存位置: {self.temp_dir}")

            # 显示文件列表（不再按城市分组，因为文件名现在包含完整备注）
            print(f"\n📋 保留的文件列表:")
            for i, (temp_file, row_index) in enumerate(existing_files, 1):
                filename = os.path.basename(temp_file)
                print(f"  {i:2d}. {filename}")
                if i >= 10 and len(existing_files) > 10:  # 如果文件很多，只显示前10个
                    remaining = len(existing_files) - 10
                    print(f"      ... 还有 {remaining} 个文件")
                    break

        if missing_files:
            print(f"\n⚠️  丢失的文件:")
            for temp_file, row_index in missing_files:
                print(f"  ❌ {os.path.basename(temp_file)} (第{row_index}条数据)")

        # 创建文件索引
        self.create_file_index(existing_files)

        print(f"\n💡 使用说明:")
        print(f"  • 临时文件已保留在: {self.temp_dir}")
        print(f"  • 文件名格式: 托运单_[完整备注内容]_[日期]_[序号].xlsx")
        print(f"  • 文件名包含完整的单身备注信息，便于识别")
        print(f"  • 可以直接打开查看或重新打印")
        print(f"  • 如需清理，请手动删除 temp_print 文件夹")

    def create_file_index(self, existing_files):
        """创建文件索引，便于用户查找"""
        try:
            index_file = os.path.join(self.temp_dir, "文件索引.txt")

            with open(index_file, 'w', encoding='utf-8') as f:
                f.write("托运单临时文件索引\n")
                f.write("=" * 50 + "\n")
                f.write(f"创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总文件数: {len(existing_files)}\n")
                f.write("=" * 50 + "\n\n")

                # 按序号排序显示所有文件
                sorted_files = sorted(existing_files, key=lambda x: x[1])  # 按行索引排序

                f.write("📋 文件列表 (按处理顺序排序)\n")
                f.write("-" * 50 + "\n")
                for temp_file, row_index in sorted_files:
                    filename = os.path.basename(temp_file)
                    f.write(f"第{row_index:3d}条数据: {filename}\n")
                    f.write(f"    完整路径: {temp_file}\n")

                    # 尝试从文件名中提取备注信息
                    if '托运单_' in filename and '_20' in filename:
                        # 提取备注部分（去掉"托运单_"前缀和日期后缀）
                        try:
                            parts = filename.replace('.xlsx', '').split('_')
                            if len(parts) >= 3:
                                # 重新组合备注内容（去掉第一个"托运单"和最后两个部分"日期_序号"）
                                remark_parts = parts[1:-2]  # 去掉"托运单"和"日期_序号"
                                if remark_parts:
                                    remark = '_'.join(remark_parts).replace('_', ' ')
                                    f.write(f"    备注信息: {remark}\n")
                        except:
                            pass
                    f.write("\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write("说明:\n")
                f.write("• 每个文件对应一张托运单\n")
                f.write("• 文件名包含完整的单身备注信息和日期\n")
                f.write("• 文件名格式: 托运单_[完整备注内容]_[日期]_[序号].xlsx\n")
                f.write("• 可以直接双击打开查看内容\n")
                f.write("• 如需重新打印，可以右键选择打印\n")
                f.write("• 备注信息已经过清理，移除了不适合文件名的特殊字符\n")

            print(f"  📋 已创建文件索引: {index_file}")

        except Exception as e:
            print(f"  ⚠️ 创建文件索引失败: {e}")

    def cleanup_temp_files(self, temp_files):
        """智能清理临时文件（处理文件锁定问题）- 保留作为备用方法"""
        print(f"\n{'='*60}")
        print("开始智能清理临时文件...")
        print(f"{'='*60}")

        cleaned_count = 0
        locked_files = []

        # 第一轮：立即尝试删除
        print("第一轮清理：立即删除可用文件...")
        for temp_file, _ in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    cleaned_count += 1
                    if cleaned_count % 10 == 0:  # 每10个文件显示一次进度
                        print(f"  已删除 {cleaned_count} 个文件...")
            except PermissionError as e:
                if "WinError 32" in str(e) or "另一个程序正在使用此文件" in str(e):
                    locked_files.append(temp_file)
                else:
                    print(f"  ✗ 删除失败: {os.path.basename(temp_file)} - {str(e)}")
            except Exception as e:
                print(f"  ✗ 删除失败: {os.path.basename(temp_file)} - {str(e)}")

        # 第二轮：等待并重试锁定的文件
        if locked_files:
            print(f"\n第二轮清理：等待并重试 {len(locked_files)} 个锁定文件...")
            print("等待打印机释放文件锁定...")

            max_retries = 3
            retry_delay = 2  # 秒

            for retry in range(max_retries):
                if not locked_files:
                    break

                print(f"  重试 {retry + 1}/{max_retries}...")
                time.sleep(retry_delay)

                remaining_locked = []
                for temp_file in locked_files:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                            cleaned_count += 1
                    except PermissionError:
                        remaining_locked.append(temp_file)
                    except Exception as e:
                        print(f"    ✗ 重试失败: {os.path.basename(temp_file)} - {str(e)}")

                locked_files = remaining_locked
                retry_delay += 1  # 逐渐增加等待时间

        # 第三轮：创建延迟清理任务
        if locked_files:
            print(f"\n第三轮处理：{len(locked_files)} 个文件仍被锁定，创建延迟清理任务...")
            self.create_delayed_cleanup_task(locked_files)

        # 尝试删除临时目录
        try:
            if os.path.exists(self.temp_dir):
                remaining_files = os.listdir(self.temp_dir)
                if not remaining_files:
                    os.rmdir(self.temp_dir)
                    print("✓ 已删除临时目录")
                else:
                    print(f"ℹ️  临时目录保留（包含 {len(remaining_files)} 个文件）")
        except Exception as e:
            print(f"清理临时目录时出错: {str(e)}")

        total_files = len(temp_files)
        success_rate = (cleaned_count / total_files * 100) if total_files > 0 else 0

        print(f"\n清理统计:")
        print(f"  总文件数: {total_files}")
        print(f"  成功删除: {cleaned_count}")
        print(f"  仍被锁定: {len(locked_files)}")
        print(f"  清理成功率: {success_rate:.1f}%")

        if locked_files:
            print(f"\n⚠️  注意: {len(locked_files)} 个文件因被其他程序使用而无法立即删除")
            print("   这些文件将在系统空闲时自动清理")

    def create_delayed_cleanup_task(self, locked_files):
        """创建延迟清理任务"""
        try:
            cleanup_script_path = os.path.join(self.temp_dir, "delayed_cleanup.bat")

            with open(cleanup_script_path, 'w', encoding='utf-8') as f:
                f.write("@echo off\n")
                f.write("echo 延迟清理临时文件...\n")
                f.write("timeout /t 10 /nobreak >nul\n")  # 等待10秒

                for temp_file in locked_files:
                    f.write(f'if exist "{temp_file}" (\n')
                    f.write(f'    del /f /q "{temp_file}" 2>nul\n')
                    f.write(f')\n')

                f.write(f'if exist "{self.temp_dir}" (\n')
                f.write(f'    rmdir /q "{self.temp_dir}" 2>nul\n')
                f.write(f')\n')
                f.write(f'del /f /q "{cleanup_script_path}" 2>nul\n')  # 删除自己

            # 启动延迟清理任务（后台运行）
            import subprocess
            subprocess.Popen([cleanup_script_path],
                           creationflags=subprocess.CREATE_NO_WINDOW,
                           shell=True)

            print(f"  ✓ 已创建延迟清理任务: {len(locked_files)} 个文件将在10秒后重试删除")

        except Exception as e:
            print(f"  ✗ 创建延迟清理任务失败: {str(e)}")

    def print_final_stats(self):
        """打印最终统计信息"""
        stats = self.monitor.get_stats()

        print(f"\n{'='*60}")
        print("批量打印处理完成！最终统计:")
        print(f"{'='*60}")
        print(f"处理时间: {stats['elapsed_time']:.1f} 秒")
        print(f"处理速率: {stats['processing_rate']:.1f} 条/秒")
        print(f"总数据条数: {stats['total_processed']}")
        print(f"成功处理: {stats['successful']}")
        print(f"处理失败: {stats['failed']}")
        print(f"跳过数据: {stats['skipped']}")
        print(f"打印成功: {stats['print_success']}")
        print(f"打印失败: {stats['print_failed']}")

        if stats['total_processed'] > 0:
            success_rate = (stats['successful'] / stats['total_processed']) * 100
            print(f"处理成功率: {success_rate:.1f}%")

        if stats['print_success'] + stats['print_failed'] > 0:
            print_success_rate = (stats['print_success'] / (stats['print_success'] + stats['print_failed'])) * 100
            print(f"打印成功率: {print_success_rate:.1f}%")

        print(f"{'='*60}")

# 数据处理类
class DataProcessor:
    """数据处理主类"""

    def __init__(self, current_dir):
        self.current_dir = current_dir
        self.df = None
        self.original_columns = []

    def find_excel_files(self):
        """查找当前目录下的Excel文件（排除临时文件）"""
        excel_files = [f for f in os.listdir(self.current_dir)
                      if f.endswith('.xlsx')
                      and f not in Config.EXCLUDED_FILES
                      and not f.startswith('~$')  # 排除Excel临时文件
                      and not f.startswith('.')   # 排除隐藏文件
                      ]

        if not excel_files:
            print("当前目录下没有找到Excel文件！")
            sys.exit(1)

        print(f"找到 {len(excel_files)} 个Excel文件:")
        for i, file in enumerate(excel_files, 1):
            print(f"  {i}. {file}")

        return excel_files

    def load_excel_data(self, excel_file):
        """加载Excel数据（完全后台模式，不启动Excel）"""
        try:
            print(f"正在后台加载Excel数据: {excel_file}")
            print("注意：使用openpyxl后台读取，不会启动Excel应用程序")

            # 使用openpyxl在后台读取数据，不会打开Excel界面
            import openpyxl

            # 以只读模式打开工作簿，不启动Excel应用程序
            wb = openpyxl.load_workbook(excel_file, read_only=True, data_only=True)
            ws = wb.active

            # 获取列名（第一行）
            columns = []
            for cell in ws[1]:
                if cell.value is not None:
                    columns.append(str(cell.value))
                else:
                    break

            print(f"检测到列名: {columns}")

            # 读取所有数据
            data_rows = []
            total_rows = ws.max_row

            for row_num in range(2, total_rows + 1):
                row_data = {}
                for col_num, col_name in enumerate(columns, 1):
                    cell_value = ws.cell(row=row_num, column=col_num).value

                    # 特殊处理料号列，确保为字符串格式
                    if '料号' in col_name and cell_value is not None:
                        row_data[col_name] = str(cell_value)
                    else:
                        row_data[col_name] = cell_value

                data_rows.append(row_data)

            # 关闭工作簿
            wb.close()

            # 创建DataFrame
            self.df = pd.DataFrame(data_rows)

            # 确保料号列是字符串类型
            for col in self.df.columns:
                if '料号' in col:
                    self.df[col] = self.df[col].astype(str)
                    print(f"已将 {col} 列转换为字符串格式")

            # 显示原始数据信息
            print(f"✓ 数据加载完成（后台模式）")
            print(f"  原始数据行数: {len(self.df)}")
            print(f"  原始数据列数: {len(self.df.columns)}")
            print(f"  列名: {list(self.df.columns)}")
            print("  整个过程未显示Excel界面")

            return True

        except Exception as e:
            print(f"✗ 后台加载Excel数据失败: {str(e)}")
            return False

    def enhance_data_processing(self):
        """完整的数据增强处理"""
        if self.df is None:
            print("❌ 没有数据可处理")
            return None

        print("\n🔄 开始数据增强处理...")
        print("=" * 50)

        df = self.df.copy()

        # 1. 确保料号列是字符串类型
        print("📝 1. 处理料号格式...")
        if '料号' in df.columns:
            df['料号'] = df['料号'].astype(str)
            print(f"   ✓ 料号列已转换为字符串格式")

        # 2. 产品类型识别
        print("🎯 2. 产品类型识别...")
        if '产品类型' not in df.columns:
            if '料号' in df.columns:
                df['产品类型'] = df['料号'].apply(Utils.determine_product_type)
                type_counts = df['产品类型'].value_counts()
                print(f"   ✓ 产品类型识别完成:")
                for product_type, count in type_counts.items():
                    if product_type:  # 只显示非空的产品类型
                        print(f"     - {product_type}: {count}个")
            else:
                df['产品类型'] = ''
                print("   ⚠️ 未找到料号列，产品类型设为空")

        # 3. 公司信息匹配和增强
        print("🏢 3. 公司信息匹配...")
        if '公司主体' in df.columns:
            # 替换公司主体为公司全称
            original_companies = df['公司主体'].value_counts()
            print(f"   📋 原始公司主体:")
            for company, count in original_companies.items():
                if company:
                    print(f"     - {company}: {count}个")

            df['公司主体'] = df['公司主体'].apply(self.get_full_company_name)

            updated_companies = df['公司主体'].value_counts()
            print(f"   ✓ 公司主体替换为全称完成:")
            for company, count in updated_companies.items():
                if company:
                    print(f"     - {company}: {count}个")

            # 添加制单人列
            if '制单人' not in df.columns:
                df['制单人'] = df['公司主体'].apply(self.get_operator_by_company)
                operator_counts = df['制单人'].value_counts()
                print(f"   ✓ 制单人分配完成:")
                for operator, count in operator_counts.items():
                    if operator:
                        print(f"     - {operator}: {count}个")

            # 添加物流公司列
            if '物流公司' not in df.columns:
                df['物流公司'] = df['公司主体'].apply(self.get_logistics_by_company)
                logistics_counts = df['物流公司'].value_counts()
                print(f"   ✓ 物流公司分配完成:")
                for logistics, count in logistics_counts.items():
                    if logistics:
                        print(f"     - {logistics}: {count}个")

        # 4. 目的地提取
        print("📍 4. 目的地信息提取...")
        if '目的地' not in df.columns:
            if '单身备注' in df.columns:
                df['目的地'] = df['单身备注'].apply(self.extract_destination)
                dest_counts = df['目的地'].value_counts()
                print(f"   ✓ 目的地提取完成:")
                for dest, count in dest_counts.items():
                    if dest:
                        print(f"     - {dest}: {count}个")
            else:
                df['目的地'] = ''
                print("   ⚠️ 未找到单身备注列，目的地设为空")

        # 5. 方数计算（如果不存在）
        print("� 5. 方数计算...")
        if '方数' not in df.columns:
            df['方数'] = 0
            print("   ✓ 方数列不存在，默认设为空")
        else:
            print("   ✓ 方数列已存在，保持原值")

        # 6. 交货方式确定（如果不存在）
        print("🚚 6. 交货方式确定...")
        if '交货方式' not in df.columns:
            df['交货方式'] = '送货'
            print("   ✓ 交货方式列不存在，默认设为'送货'")
        else:
            print("   ✓ 交货方式列已存在，保持原值")

        # 7. 数据验证
        print("✅ 7. 数据完整性验证...")
        missing_data = self.validate_processed_data(df)
        if missing_data:
            print("   ⚠️ 发现缺失数据:")
            for col, count in missing_data.items():
                print(f"     - {col}: {count}行缺失")
        else:
            print("   ✓ 所有必要数据完整")

        print("=" * 50)

        # 8. 合并重复行（相同单身备注和公司主体的行）
        print("🔄 8. 合并重复行...")
        original_row_count = len(df)

        # 1. 对单身备注列和公司主体分别升序
        df = df.sort_values(by=['单身备注', '公司主体'])

        # 2. 当发现单身备注列和公司主体列同时重复时，会自动合并这些行
        # 3. 在合并过程中，出货数量列和出货件数列的值会分别累加
        # 4. 其他列的值会保留第一个非空值

        # 定义合并函数，对数值列求和，其他列取第一个非空值
        def custom_agg(x):
            result = {}
            # 对出货数量、出货件数和方数列求和
            result['出货数量'] = x['出货数量'].sum()
            result['出货件数'] = x['出货件数'].sum()
            result['方数'] = x['方数'].sum()

            # 对其他列取第一个非空值
            for col in x.columns:
                if col not in ['出货数量', '出货件数', '方数']:
                    non_empty = x[col].dropna()
                    result[col] = non_empty.iloc[0] if len(non_empty) > 0 else None

            return pd.Series(result)

        # 确保数值列是数值类型
        try:
            if '出货数量' in df.columns:
                df['出货数量'] = pd.to_numeric(df['出货数量'], errors='coerce').fillna(0)
            if '出货件数' in df.columns:
                df['出货件数'] = pd.to_numeric(df['出货件数'], errors='coerce').fillna(0)
            if '方数' in df.columns:
                df['方数'] = pd.to_numeric(df['方数'], errors='coerce').fillna(0)
        except Exception as e:
            print(f"   ⚠️ 转换数值列时出错: {str(e)}")

        # 保存原始列顺序
        original_columns = df.columns.tolist()
        print(f"   📋 保存原始列顺序: {len(original_columns)}列")

        # 按单身备注和公司主体分组并应用自定义合并函数
        try:
            print("   🔄 开始合并重复行...")
            df = df.groupby(['单身备注', '公司主体'], as_index=False).apply(custom_agg)
            # 恢复原始列顺序
            df = df.reindex(columns=original_columns)
            merged_row_count = len(df)
            print(f"   ✅ 合并完成，原始行数: {original_row_count}，合并后行数: {merged_row_count}")
            if original_row_count > merged_row_count:
                print(f"   📊 成功合并了 {original_row_count - merged_row_count} 行重复数据")
        except Exception as e:
            print(f"   ⚠️ 合并行时出错: {str(e)}")
            print("   🔄 尝试使用替代方法合并...")
            # 替代方法：手动合并
            result_rows = []
            for (note, company), group in df.groupby(['单身备注', '公司主体']):
                row = {}
                # 初始化所有列为None，确保所有原始列都存在
                for col in original_columns:
                    row[col] = None

                # 设置分组键
                row['单身备注'] = note
                row['公司主体'] = company

                # 设置需要求和的列
                row['出货数量'] = group['出货数量'].sum() if '出货数量' in group.columns else 0
                row['出货件数'] = group['出货件数'].sum() if '出货件数' in group.columns else 0
                row['方数'] = group['方数'].sum() if '方数' in group.columns else 0

                # 其他列取第一个非空值
                for col in group.columns:
                    if col not in ['单身备注', '公司主体', '出货数量', '出货件数', '方数']:
                        non_empty = group[col].dropna()
                        row[col] = non_empty.iloc[0] if len(non_empty) > 0 else None

                result_rows.append(row)

            df = pd.DataFrame(result_rows)
            # 恢复原始列顺序
            df = df.reindex(columns=original_columns)
            merged_row_count = len(df)
            print(f"   ✅ 替代方法合并完成，原始行数: {original_row_count}，合并后行数: {merged_row_count}")
            if original_row_count > merged_row_count:
                print(f"   📊 成功合并了 {original_row_count - merged_row_count} 行重复数据")

        print("=" * 50)
        print("🎉 数据增强处理完成！")
        print(f"📊 处理结果: {len(df)}行数据，{len(df.columns)}列")

        return df

    def get_full_company_name(self, company_name):
        """根据公司名称获取公司全称"""
        if pd.isna(company_name):
            return company_name

        # 如果已经是全称，直接返回
        company_str = str(company_name).strip()
        if len(company_str) > 10:  # 简单判断：如果名称较长，可能已经是全称
            return company_str

        # 从配置中获取公司信息
        company_info = Utils.get_company_info(company_str)
        if company_info and 'full_name' in company_info:
            return company_info['full_name']

        # 如果没有找到映射，返回原名称
        return company_str

    def get_operator_by_company(self, company_name):
        """根据公司名称获取制单人（支持全称和简称）"""
        if pd.isna(company_name):
            return '系统默认'

        company_str = str(company_name).strip()

        # 先尝试直接匹配
        company_info = Utils.get_company_info(company_str)
        if company_info and 'operator' in company_info:
            return company_info['operator']

        # 如果是全称，尝试通过全称反向查找
        for key, info in Config.COMPANY_MAPPING.items():
            if info.get('full_name') == company_str:
                return info.get('operator', '系统默认')

        return '系统默认'

    def get_logistics_by_company(self, company_name):
        """根据公司名称获取物流公司（支持全称和简称）"""
        if pd.isna(company_name):
            return '默认物流公司'

        company_str = str(company_name).strip()

        # 先尝试直接匹配
        company_info = Utils.get_company_info(company_str)
        if company_info and 'logistics' in company_info:
            return company_info['logistics']

        # 如果是全称，尝试通过全称反向查找
        for key, info in Config.COMPANY_MAPPING.items():
            if info.get('full_name') == company_str:
                return info.get('logistics', '默认物流公司')

        return '默认物流公司'

    def extract_destination(self, remark):
        """从单身备注中提取目的地"""
        if pd.isna(remark):
            return ''

        remark_str = str(remark).strip()
        if not remark_str:
            return ''

        # 提取第一个词作为目的地
        words = remark_str.split()
        if words:
            destination = words[0]
            # 清理常见的非地名词汇
            if destination not in ['紧急', '普通', '重要', '新', 'VIP', '大', '重点', '优质']:
                return destination

        return ''



    def validate_processed_data(self, df):
        """验证处理后的数据完整性"""
        missing_data = {}

        # 检查关键列的缺失情况
        key_columns = ['产品类型', '目的地', '制单人', '方数', '交货方式']

        for col in key_columns:
            if col in df.columns:
                missing_count = df[col].isna().sum() + (df[col] == '').sum()
                if missing_count > 0:
                    missing_data[col] = missing_count

        return missing_data

def main():
    """主函数（优化版）"""
    print("托运单处理程序启动（优化版）...")
    print(f"打印功能状态: {'启用' if can_print else '禁用'}")

    # 启动Excel资源监控（已禁用以保护用户Excel）
    # if Config.PRINT_STRATEGY_CONFIG['enable_print_monitoring']:
    #     excel_monitor.start_monitoring()
    #     print("📊 Excel资源监控已启动，确保不影响用户Excel使用")

    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"当前工作目录: {current_dir}")

    # 创建数据处理器
    processor = DataProcessor(current_dir)

    # 查找Excel文件
    excel_files = processor.find_excel_files()
    excel_file = os.path.join(current_dir, excel_files[0])
    print(f"处理文件: {excel_file}")

    # 加载数据
    if not processor.load_excel_data(excel_file):
        sys.exit(1)

    print("数据加载完成，开始完整数据处理...")

    # 执行完整的数据增强处理
    df = processor.enhance_data_processing()

    print(f"完整处理后数据行数: {len(df)}")
    print("数据增强处理完成，包含以下增强内容:")
    print("  ✓ 产品类型识别")
    print("  ✓ 公司信息匹配")
    print("  ✓ 目的地提取")
    print("  ✓ 制单人分配")
    print("  ✓ 方数计算")
    print("  ✓ 交货方式确定")

    # 保存处理后的完整数据到Excel文件
    output_file = os.path.join(current_dir, Config.OUTPUT_FILE)
    try:
        print(f"\n💾 保存处理后的数据到: {Config.OUTPUT_FILE}")
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"✅ 处理后的数据已保存到: {output_file}")
        print(f"📊 保存的数据包含 {len(df)} 行，{len(df.columns)} 列")
        print("📋 保存的列包括:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
    except Exception as e:
        print(f"❌ 保存处理后数据失败: {e}")
        return

    # 查找模板文件并开始批量打印
    if can_print:
        template_files = [os.path.join(current_dir, f) for f in Config.TEMPLATE_FILES]
        template_file = None
        for file_path in template_files:
            if os.path.exists(file_path):
                template_file = file_path
                print(f"找到模板文件: {template_file}")
                break

        if template_file:
            print(f"\n🖨️ 开始批量打印处理后的托运单数据...")
            print(f"📄 打印数据来源: {Config.OUTPUT_FILE}")
            print(f"📋 打印模板: {os.path.basename(template_file)}")

            # 创建批量打印处理器
            batch_processor = BatchPrintProcessor(current_dir)

            # 执行优化的批量打印（使用处理后的数据）
            batch_processor.optimized_batch_print(df, template_file)
        else:
            print("未找到模板文件，跳过打印")
    else:
        print("打印功能未启用，跳过打印处理")

    # 停止Excel资源监控（已禁用）
    # if Config.PRINT_STRATEGY_CONFIG['enable_print_monitoring']:
    #     excel_monitor.stop_monitoring()
    #     print("📊 Excel资源监控已停止")

    # 强制清理所有Excel资源（已禁用以保护用户Excel）
    # excel_monitor.force_cleanup_excel_resources()

    print("\n🎉 程序执行完毕！用户可正常使用Excel")

# GUI界面类
class ShippingOrderGUI:
    """托运单处理GUI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚚 七彩虹托运单批量处理系统 V3.3.3")
        self.root.geometry("1000x780")  # 优化窗口尺寸，协调日志窗口大小
        self.root.resizable(True, True)

        # 设置窗口背景色
        self.root.configure(bg="#F8F9FA")

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 设置最小窗口大小，确保内容完全显示
        self.root.minsize(900, 680)

        # 变量
        self.selected_file = tk.StringVar()
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")

        # 创建界面
        self.create_widgets()

        # 日志队列，用于线程间通信
        self.log_queue = gui_queue.Queue()

        # 处理线程
        self.processing_thread = None
        self.is_processing = False

        # 初始化print重定向器
        self.print_redirector = None
        
        # 重定向print输出到GUI日志
        self.setup_print_redirect()

        # 启动日志更新
        self.update_log()
        
        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.cleanup)

        # 初始化日志
        self.log_message("🚚 托运单批量处理系统 V3.3.3 已启动", "SUCCESS")
        self.log_message("=" * 50, "INFO")
        self.log_message("👨‍💻 开发者：李志魁", "INFO")
        self.log_message("📅 开发时间：2025/7/6", "INFO")
        self.log_message("🏷️ 版本号：V3.3.3", "INFO")
        self.log_message("=" * 50, "INFO")
        self.log_message("请选择Excel文件开始处理", "INFO")

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()

        # 设置主题
        try:
            style.theme_use('clam')  # 使用clam主题，支持更好的自定义
        except:
            pass

        # 配置按钮样式
        # 浏览按钮 - 蓝色
        style.configure("Browse.TButton",
                       background="#4A90E2",
                       foreground="white",
                       font=("Microsoft YaHei", 10, "bold"),
                       padding=(15, 8),
                       relief="flat")
        style.map("Browse.TButton",
                 background=[('active', '#357ABD'),
                           ('pressed', '#2E6DA4')])

        # 批量打单按钮 - 绿色
        style.configure("Process.TButton",
                       background="#5CB85C",
                       foreground="white",
                       font=("Microsoft YaHei", 12, "bold"),
                       padding=(20, 10),
                       relief="flat")
        style.map("Process.TButton",
                 background=[('active', '#449D44'),
                           ('pressed', '#398439'),
                           ('disabled', '#CCCCCC')])

        # 停止按钮 - 橙色
        style.configure("Stop.TButton",
                       background="#F0AD4E",
                       foreground="white",
                       font=("Microsoft YaHei", 10, "bold"),
                       padding=(15, 8),
                       relief="flat")
        style.map("Stop.TButton",
                 background=[('active', '#EC971F'),
                           ('pressed', '#D58512'),
                           ('disabled', '#CCCCCC')])

        # 清空日志按钮 - 灰色
        style.configure("Clear.TButton",
                       background="#6C757D",
                       foreground="white",
                       font=("Microsoft YaHei", 10),
                       padding=(15, 8),
                       relief="flat")
        style.map("Clear.TButton",
                 background=[('active', '#5A6268'),
                           ('pressed', '#495057')])

        # 配置进度条样式
        style.configure("Custom.Horizontal.TProgressbar",
                       background="#5CB85C",
                       troughcolor="#E9ECEF",
                       borderwidth=0,
                       lightcolor="#5CB85C",
                       darkcolor="#5CB85C")

        # 配置标签框样式
        style.configure("Custom.TLabelframe",
                       background="#F8F9FA",
                       relief="solid",
                       borderwidth=1)
        style.configure("Custom.TLabelframe.Label",
                       background="#F8F9FA",
                       foreground="#495057",
                       font=("Microsoft YaHei", 10, "bold"))

        # 配置输入框样式
        style.configure("Custom.TEntry",
                       fieldbackground="white",
                       borderwidth=1,
                       relief="solid")

    def add_button_hover_effects(self):
        """添加按钮悬停效果"""
        def on_enter(event, button):
            button.configure(cursor="hand2")

        def on_leave(event, button):
            button.configure(cursor="")

        # 为所有按钮添加悬停效果
        buttons = [self.browse_btn, self.process_btn, self.stop_btn, self.clear_btn]

        for button in buttons:
            button.bind("<Enter>", lambda e, b=button: on_enter(e, b))
            button.bind("<Leave>", lambda e, b=button: on_leave(e, b))

    def create_widgets(self):
        """创建界面组件"""
        # 设置主题样式
        self.setup_styles()

        # 主框架
        main_frame = ttk.Frame(self.root, padding="12")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)  # 日志区域可扩展

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 文件选择", padding="12", style="Custom.TLabelframe")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 12))
        file_frame.columnconfigure(1, weight=1)

        ttk.Label(file_frame, text="Excel文件:", font=("Microsoft YaHei", 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 15))

        self.file_entry = ttk.Entry(file_frame, textvariable=self.selected_file, state="readonly",
                                   font=("Microsoft YaHei", 10), width=60)
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))

        self.browse_btn = ttk.Button(file_frame, text="📂 浏览", command=self.browse_file, style="Browse.TButton")
        self.browse_btn.grid(row=0, column=2, sticky=tk.W)

        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=(0, 12))

        # 主要操作按钮
        self.process_btn = ttk.Button(button_frame, text="🚀 批量打单", command=self.start_processing,
                                    style="Process.TButton")
        self.process_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 次要操作按钮
        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止处理", command=self.stop_processing,
                                 state="disabled", style="Stop.TButton")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.clear_btn = ttk.Button(button_frame, text="🗑️ 清空日志", command=self.clear_log, style="Clear.TButton")
        self.clear_btn.pack(side=tk.LEFT)

        # 进度条区域
        progress_frame = ttk.LabelFrame(main_frame, text="📊 处理进度", padding="12", style="Custom.TLabelframe")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 12))
        progress_frame.columnconfigure(0, weight=1)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, mode='determinate', style="Custom.Horizontal.TProgressbar")
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))

        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var,
                                    font=("Microsoft YaHei", 10), foreground="#495057")
        self.status_label.grid(row=1, column=0, sticky=tk.W)

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="📝 处理日志", padding="12", style="Custom.TLabelframe")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 创建带滚动条的文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=16, width=85,
                                                wrap=tk.WORD, state=tk.DISABLED,
                                                font=("Consolas", 9),
                                                background="#FAFAFA",
                                                relief="solid",
                                                borderwidth=1)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置文本标签样式
        self.log_text.tag_configure("INFO", foreground="#495057")
        self.log_text.tag_configure("SUCCESS", foreground="#28A745", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("WARNING", foreground="#FFC107", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("ERROR", foreground="#DC3545", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("STEP", foreground="#007BFF", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("DETAIL", foreground="#6C757D", font=("Consolas", 8))

        # 开发者信息区域
        dev_frame = ttk.Frame(main_frame)
        dev_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(8, 0))

        # 创建开发者信息标签
        dev_info_frame = ttk.Frame(dev_frame)
        dev_info_frame.pack(fill=tk.X)

        # 左侧开发者信息
        dev_left_frame = ttk.Frame(dev_info_frame)
        dev_left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        dev_label = ttk.Label(dev_left_frame,
                             text="👨‍💻 开发者：李志魁",
                             font=("Microsoft YaHei", 9, "bold"),
                             foreground="#007BFF")
        dev_label.pack(side=tk.LEFT)

        # 中间开发时间
        dev_center_frame = ttk.Frame(dev_info_frame)
        dev_center_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        time_label = ttk.Label(dev_center_frame,
                              text="📅 开发时间：2025/5/6",
                              font=("Microsoft YaHei", 9),
                              foreground="#6C757D")
        time_label.pack()

        # 右侧版本信息
        dev_right_frame = ttk.Frame(dev_info_frame)
        dev_right_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        version_label = ttk.Label(dev_right_frame,
                                 text="🏷️ 版本号：V3.2",
                                 font=("Microsoft YaHei", 9, "bold"),
                                 foreground="#28A745")
        version_label.pack(side=tk.RIGHT)

        # 添加分隔线
        separator = ttk.Separator(dev_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(5, 0))

        # 添加按钮悬停效果
        self.add_button_hover_effects()

    def browse_file(self):
        """浏览并选择Excel文件"""
        file_types = [
            ("Excel文件", "*.xlsx *.xls *.xlsm"),
            ("所有文件", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )

        if filename:
            self.selected_file.set(filename)
            self.log_message(f"已选择文件: {os.path.basename(filename)}", "SUCCESS")
            self.log_message(f"文件路径: {filename}", "DETAIL")

    def setup_print_redirect(self):
        """设置print输出重定向到GUI日志"""
        import sys
        import atexit

        class PrintRedirector:
            def __init__(self, gui_logger):
                self.gui_logger = gui_logger
                self.original_stdout = sys.stdout
                self.original_stderr = sys.stderr
                
                # 保存原始的stdout和stderr
                sys.stdout = self
                sys.stderr = self
                
            def __del__(self):
                # 恢复原始的stdout和stderr
                try:
                    sys.stdout = self.original_stdout
                    sys.stderr = self.original_stderr
                except:
                    pass

            def write(self, text):
                if text and text.strip():  # 只记录非空内容
                    # 判断消息类型
                    if "错误" in text or "失败" in text or "Error" in text:
                        level = "ERROR"
                    elif "警告" in text or "Warning" in text:
                        level = "WARNING"
                    elif "成功" in text or "完成" in text or "Success" in text:
                        level = "SUCCESS"
                    elif "开始" in text or "正在" in text:
                        level = "STEP"
                    else:
                        level = "INFO"

                    try:
                        if self.gui_logger:
                            self.gui_logger(text.strip(), level)
                    except:
                        pass  # 忽略日志记录失败

                # 同时输出到原始stdout（用于调试）
                try:
                    if self.original_stdout and not self.original_stdout.closed:
                        self.original_stdout.write(text)
                except:
                    pass

            def flush(self):
                # 实现flush方法
                try:
                    if self.original_stdout and not self.original_stdout.closed:
                        self.original_stdout.flush()
                except:
                    pass

        # 创建并保存PrintRedirector实例
        self.print_redirector = PrintRedirector(self.log_message)

    def cleanup(self):
        """清理资源并关闭窗口"""
        try:
            # 停止处理线程
            if self.processing_thread and self.processing_thread.is_alive():
                self.stop_processing()
                self.processing_thread.join(timeout=2)

            # 恢复标准输出
            if self.print_redirector:
                sys.stdout = self.print_redirector.original_stdout
                sys.stderr = self.print_redirector.original_stderr
                self.print_redirector = None

            # 销毁窗口
            self.root.destroy()
        except Exception as e:
            print(f"清理资源时出错: {e}")

            def flush(self):
                self.original_stdout.flush()

        # 重定向stdout
        sys.stdout = PrintRedirector(self.log_message)

    def log_message(self, message, level="INFO"):
        """添加日志消息到队列"""
        try:
            # 确保消息是字符串类型
            message = str(message).strip()
            if not message:
                return

            # 添加时间戳
            timestamp = time.strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}\n"

            # 安全地将消息添加到队列
            if hasattr(self, 'log_queue') and self.log_queue is not None:
                try:
                    self.log_queue.put_nowait((formatted_message, level))
                except gui_queue.Full:
                    # 如果队列满了，直接打印到控制台
                    print(f"[{level}] {message}")
            else:
                # 如果队列不可用，打印到控制台
                print(f"[{level}] {message}")

        except Exception as e:
            # 如果出现任何错误，尝试打印到控制台
            try:
                print(f"[ERROR] 日志记录失败: {str(e)}")
                print(f"[{level}] {message}")
            except:
                pass

    def update_log(self):
        """更新日志显示"""
        try:
            # 检查日志队列是否存在
            if not hasattr(self, 'log_queue') or self.log_queue is None:
                return
                
            # 处理队列中的所有消息
            messages_processed = 0
            max_messages_per_update = 20  # 每次更新最多处理的消息数，避免GUI阻塞
            
            while messages_processed < max_messages_per_update:
                try:
                    message, level = self.log_queue.get_nowait()
                    
                    # 确保文本控件存在
                    if not hasattr(self, 'log_text') or self.log_text is None:
                        break
                        
                    try:
                        # 启用文本框编辑
                        self.log_text.config(state=tk.NORMAL)

                        # 插入消息
                        self.log_text.insert(tk.END, message, level)

                        # 自动滚动到底部
                        self.log_text.see(tk.END)

                        # 禁用文本框编辑
                        self.log_text.config(state=tk.DISABLED)
                        
                        messages_processed += 1
                    except Exception as text_error:
                        print(f"更新日志文本框失败: {str(text_error)}")
                        break
                        
                except gui_queue.Empty:
                    break
                except Exception as queue_error:
                    print(f"处理日志队列时出错: {str(queue_error)}")
                    break

        except Exception as e:
            print(f"更新日志显示时出错: {str(e)}")
        finally:
            # 确保GUI仍然存在
            if hasattr(self, 'root') and self.root is not None and self.root.winfo_exists():
                # 每100毫秒检查一次
                self.root.after(100, self.update_log)

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message("日志已清空", "INFO")

    def start_processing(self):
        """开始处理数据"""
        try:
            if not self.selected_file.get():
                messagebox.showerror("错误", "请先选择Excel文件！")
                return

            if not os.path.exists(self.selected_file.get()):
                messagebox.showerror("错误", "选择的文件不存在！")
                return

            if self.is_processing:
                messagebox.showwarning("警告", "正在处理中，请等待完成！")
                return

            # 更新界面状态
            self.is_processing = True
            self.process_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.browse_btn.config(state="disabled")

            # 重置进度条和状态
            self.progress_var.set(0)
            self.status_var.set("正在处理...")

            # 清空日志
            self.clear_log()
            self.log_message("开始处理数据...", "STEP")
            self.log_message(f"选择的文件: {self.selected_file.get()}", "INFO")

            # 启动处理线程
            self.processing_thread = threading.Thread(
                target=self.process_file,
                daemon=True
            )
            self.processing_thread.start()
            
            # 启动进度监控
            self.root.after(500, self.check_processing_status)
            
        except Exception as e:
            self.log_message(f"启动处理失败: {str(e)}", "ERROR")
            self.reset_processing_state()

    def stop_processing(self):
        """停止处理"""
        if self.is_processing:
            self.log_message("正在停止处理...", "WARNING")
            self.is_processing = False
            # 注意：实际的停止逻辑需要在处理线程中检查self.is_processing标志

    def process_file(self):
        """处理文件的主要逻辑（在后台线程中运行）"""
        try:
            file_path = self.selected_file.get()
            self.log_message("=" * 60, "STEP")
            self.log_message("开始批量处理托运单", "STEP")
            self.log_message("=" * 60, "STEP")
            self.log_message(f"处理文件: {os.path.basename(file_path)}", "INFO")
            self.log_message(f"完整路径: {file_path}", "DETAIL")

            # 获取当前目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.log_message(f"工作目录: {current_dir}", "DETAIL")

            # 步骤1：读取Excel文件
            self.log_message("步骤 1/8: 读取Excel文件", "STEP")
            self.status_var.set("正在读取Excel文件...")
            self.progress_var.set(10)

            # 检查是否被停止
            if not self.is_processing:
                self.log_message("处理已被用户停止", "WARNING")
                return

            # 读取Excel文件
            try:
                self.log_message("正在打开Excel文件...", "INFO")
                df = pd.read_excel(file_path)
                self.log_message(f"✓ 成功读取Excel文件", "SUCCESS")
                self.log_message(f"  - 数据行数: {len(df)}", "DETAIL")
                self.log_message(f"  - 数据列数: {len(df.columns)}", "DETAIL")
                self.log_message(f"  - 列名: {', '.join(df.columns.tolist())}", "DETAIL")
            except Exception as e:
                self.log_message(f"✗ 读取Excel文件失败: {str(e)}", "ERROR")
                self.finish_processing()
                return

            # 步骤2：验证数据格式
            self.log_message("步骤 2/8: 验证数据格式", "STEP")
            self.progress_var.set(20)
            self.status_var.set("正在验证数据...")

            # 检查是否被停止
            if not self.is_processing:
                self.log_message("处理已被用户停止", "WARNING")
                return

            # 验证必要的列
            self.log_message("正在检查必要的列...", "INFO")
            self.log_message(f"必要列: {', '.join(Config.REQUIRED_COLUMNS)}", "DETAIL")

            missing_columns = [col for col in Config.REQUIRED_COLUMNS if col not in df.columns]
            if missing_columns:
                error_msg = f"Excel文件缺少必要的列: {', '.join(missing_columns)}"
                self.log_message(f"✗ {error_msg}", "ERROR")
                self.log_message("请检查Excel文件格式是否正确", "ERROR")
                messagebox.showerror("数据验证失败", error_msg)
                self.finish_processing()
                return

            self.log_message("✓ 所有必要列都存在", "SUCCESS")

            # 检查数据完整性
            self.log_message("正在检查数据完整性...", "INFO")
            empty_rows = df.isnull().all(axis=1).sum()
            if empty_rows > 0:
                self.log_message(f"发现 {empty_rows} 行空数据，将自动跳过", "WARNING")

            valid_rows = len(df) - empty_rows
            self.log_message(f"有效数据行数: {valid_rows}", "DETAIL")
            self.log_message("✓ 数据验证通过", "SUCCESS")

            # 步骤3：处理数据
            self.log_message("步骤 3/8: 处理数据", "STEP")
            self.progress_var.set(30)
            self.status_var.set("正在处理数据...")

            # 检查是否被停止
            if not self.is_processing:
                self.log_message("处理已被用户停止", "WARNING")
                return

            # 处理数据
            try:
                self.log_message("开始数据处理和转换...", "INFO")
                df = self.process_data(df)
                self.log_message("✓ 数据处理完成", "SUCCESS")
            except Exception as e:
                self.log_message(f"✗ 数据处理失败: {str(e)}", "ERROR")
                self.finish_processing()
                return

            # 步骤4：查找模板文件
            self.log_message("步骤 4/8: 查找模板文件", "STEP")
            self.progress_var.set(50)
            self.status_var.set("正在查找模板文件...")

            # 检查是否被停止
            if not self.is_processing:
                self.log_message("处理已被用户停止", "WARNING")
                return

            # 查找模板文件
            self.log_message("正在搜索模板文件...", "INFO")
            self.log_message(f"搜索目录: {current_dir}", "DETAIL")

            template_file = None
            for template_name in Config.TEMPLATE_FILES:
                template_path = os.path.join(current_dir, template_name)
                self.log_message(f"检查文件: {template_name}", "DETAIL")
                if os.path.exists(template_path):
                    template_file = template_path
                    self.log_message(f"✓ 找到模板文件: {template_name}", "SUCCESS")
                    break
                else:
                    self.log_message(f"  文件不存在: {template_name}", "DETAIL")

            if not template_file:
                error_msg = "未找到模板文件，请确保以下文件之一存在于程序目录中：\n" + "\n".join(Config.TEMPLATE_FILES)
                self.log_message("✗ 未找到任何模板文件", "ERROR")
                for template_name in Config.TEMPLATE_FILES:
                    self.log_message(f"  缺失: {template_name}", "ERROR")
                messagebox.showerror("模板文件未找到", error_msg)
                self.finish_processing()
                return

            self.log_message(f"模板文件路径: {template_file}", "DETAIL")

            # 步骤5：保存处理后的数据
            self.log_message("步骤 5/8: 保存处理后的数据", "STEP")
            self.progress_var.set(60)
            self.status_var.set("正在保存处理后的数据...")

            # 检查是否被停止
            if not self.is_processing:
                self.log_message("处理已被用户停止", "WARNING")
                return

            # 保存处理后的数据
            try:
                output_file = os.path.join(current_dir, Config.OUTPUT_FILE)
                self.log_message(f"正在保存到: {Config.OUTPUT_FILE}", "INFO")
                df.to_excel(output_file, index=False)
                self.log_message(f"✓ 处理后的数据已保存", "SUCCESS")
                self.log_message(f"输出文件: {output_file}", "DETAIL")

                # 检查文件大小
                file_size = os.path.getsize(output_file)
                self.log_message(f"文件大小: {file_size:,} 字节", "DETAIL")
            except Exception as e:
                self.log_message(f"✗ 保存数据失败: {str(e)}", "ERROR")
                self.finish_processing()
                return

            # 步骤6：检查打印环境
            self.log_message("步骤 6/8: 检查打印环境", "STEP")
            self.progress_var.set(70)
            self.status_var.set("正在检查打印环境...")

            # 检查是否被停止
            if not self.is_processing:
                self.log_message("处理已被用户停止", "WARNING")
                return

            # 检查打印功能
            if not can_print:
                self.log_message("✗ 打印功能未启用，跳过打印处理", "WARNING")
                self.log_message("原因: 缺少必要的打印库 (pywin32)", "WARNING")
                self.progress_var.set(100)
                self.status_var.set("处理完成（未打印）")
                self.log_message("=" * 60, "STEP")
                self.log_message("处理完成（数据已保存，但未打印）", "SUCCESS")
                self.log_message("=" * 60, "STEP")
                self.finish_processing()
                return

            self.log_message("✓ 打印功能可用", "SUCCESS")

            # 步骤7：初始化打印处理器
            self.log_message("步骤 7/8: 初始化打印处理器", "STEP")
            try:
                self.log_message("正在初始化批量打印处理器...", "INFO")
                batch_processor = BatchPrintProcessor(current_dir)
                self.log_message("✓ 打印处理器初始化完成", "SUCCESS")
            except Exception as e:
                self.log_message(f"✗ 打印处理器初始化失败: {str(e)}", "ERROR")
                self.finish_processing()
                return

            # 步骤8：执行批量打印
            self.log_message("步骤 8/8: 执行批量打印", "STEP")
            try:
                self.log_message("开始批量打印处理...", "INFO")
                self.batch_print_with_progress(batch_processor, df, template_file)

            except Exception as e:
                self.log_message(f"✗ 批量打印失败: {str(e)}", "ERROR")
                self.finish_processing()
                return

            # 完成
            self.progress_var.set(100)
            self.status_var.set("处理完成")
            self.log_message("=" * 60, "STEP")
            self.log_message("所有处理步骤完成！", "SUCCESS")
            self.log_message("=" * 60, "STEP")
            self.finish_processing()

        except Exception as e:
            self.log_message(f"处理过程中发生错误: {str(e)}", "ERROR")
            self.finish_processing()

    def process_data(self, df):
        """完整的数据处理逻辑（整合命令行版本的完整功能）"""
        self.log_message("🔄 开始完整数据增强处理...", "STEP")
        self.log_message("=" * 50, "INFO")

        total_rows = len(df)
        self.log_message(f"准备处理 {total_rows} 条数据记录", "INFO")

        # 创建DataProcessor实例来使用完整的数据处理功能
        current_dir = os.path.dirname(os.path.abspath(self.selected_file.get()))
        processor = DataProcessor(current_dir)
        processor.df = df.copy()

        # 检查是否被停止
        if not self.is_processing:
            self.log_message("数据处理被用户停止", "WARNING")
            return df

        try:
            # 调用完整的数据增强处理方法
            self.log_message("正在执行完整的数据增强处理...", "INFO")
            enhanced_df = processor.enhance_data_processing()

            # 更新进度
            self.progress_var.set(50)

            self.log_message("✓ 完整数据增强处理完成", "SUCCESS")
            self.log_message(f"处理结果: {len(enhanced_df)}行数据，{len(enhanced_df.columns)}列", "INFO")

            # 显示增强功能列表
            self.log_message("已完成的数据增强功能:", "INFO")
            self.log_message("  ✓ 料号格式处理", "DETAIL")
            self.log_message("  ✓ 产品类型识别", "DETAIL")
            self.log_message("  ✓ 公司信息匹配和替换", "DETAIL")
            self.log_message("  ✓ 制单人自动分配", "DETAIL")
            self.log_message("  ✓ 物流公司自动分配", "DETAIL")
            self.log_message("  ✓ 目的地信息提取", "DETAIL")
            self.log_message("  ✓ 方数自动计算", "DETAIL")
            self.log_message("  ✓ 交货方式智能确定", "DETAIL")
            self.log_message("  ✓ 重复行智能合并", "DETAIL")
            self.log_message("  ✓ 数据完整性验证", "DETAIL")

            return enhanced_df

        except Exception as e:
            self.log_message(f"✗ 数据增强处理失败: {str(e)}", "ERROR")
            self.log_message("回退到基础数据处理模式...", "WARNING")

            # 回退到基础处理模式
            return self.basic_process_data(df)

    def basic_process_data(self, df):
        """基础数据处理方法（作为回退选项）"""
        self.log_message("使用基础数据处理模式...", "INFO")

        total_rows = len(df)
        processed_count = 0
        company_processed = 0
        product_processed = 0

        # 统计信息
        company_stats = {}
        product_stats = {}

        for index, row in df.iterrows():
            # 检查是否被停止
            if not self.is_processing:
                self.log_message("数据处理被用户停止", "WARNING")
                break

            current_row = index + 1

            # 处理公司主体
            if '公司主体' in row and pd.notna(row['公司主体']):
                original_company = str(row['公司主体']).strip()
                company_info = Utils.get_company_info(original_company)
                if company_info:
                    df.at[index, '公司主体'] = company_info['full_name']
                    df.at[index, '物流公司'] = company_info['logistics']
                    df.at[index, '制单人'] = company_info['operator']

                    company_processed += 1
                    company_stats[company_info['full_name']] = company_stats.get(company_info['full_name'], 0) + 1

                    if processed_count % 10 == 0:  # 每10条记录显示一次详细信息
                        self.log_message(f"  第{current_row}行: {original_company} → {company_info['full_name']}", "DETAIL")
                else:
                    self.log_message(f"  第{current_row}行: 未识别的公司 '{original_company}'", "WARNING")

            # 处理产品类型
            if '料号' in row and pd.notna(row['料号']):
                part_number = str(row['料号']).strip()
                product_type = Utils.determine_product_type(part_number)
                if product_type:
                    df.at[index, '产品类型'] = product_type
                    product_processed += 1
                    product_stats[product_type] = product_stats.get(product_type, 0) + 1

                    if processed_count % 10 == 0:  # 每10条记录显示一次详细信息
                        self.log_message(f"  第{current_row}行: 料号{part_number[:3]} → {product_type}", "DETAIL")
                else:
                    if processed_count % 10 == 0:
                        self.log_message(f"  第{current_row}行: 未识别的料号前缀 '{part_number[:3]}'", "WARNING")

            processed_count += 1

            # 更新进度（在30-50%之间）
            progress = 30 + (processed_count / total_rows) * 20
            self.progress_var.set(progress)

            # 每处理50条记录显示一次进度
            if processed_count % 50 == 0:
                self.log_message(f"已处理 {processed_count}/{total_rows} 条记录 ({processed_count/total_rows*100:.1f}%)", "INFO")

        # 显示处理统计
        self.log_message(f"基础数据处理统计:", "INFO")
        self.log_message(f"  - 总记录数: {total_rows}", "DETAIL")
        self.log_message(f"  - 已处理记录数: {processed_count}", "DETAIL")
        self.log_message(f"  - 公司信息处理: {company_processed} 条", "DETAIL")
        self.log_message(f"  - 产品类型处理: {product_processed} 条", "DETAIL")

        if company_stats:
            self.log_message("公司分布统计:", "INFO")
            for company, count in company_stats.items():
                self.log_message(f"  - {company}: {count} 条", "DETAIL")

        if product_stats:
            self.log_message("产品类型分布:", "INFO")
            for product, count in product_stats.items():
                self.log_message(f"  - {product}: {count} 条", "DETAIL")

        return df

    def batch_print_with_progress(self, batch_processor, df, template_file):
        """带进度更新的批量打印"""
        total_rows = len(df)
        self.log_message(f"准备批量打印 {total_rows} 条记录", "INFO")
        self.log_message(f"使用模板: {os.path.basename(template_file)}", "DETAIL")
        self.log_message(f"批处理大小: {Config.BATCH_SIZE}", "DETAIL")
        self.log_message(f"最大线程数: {Config.MAX_WORKERS}", "DETAIL")

        # 检查打印机状态
        self.log_message("正在检查打印机状态...", "INFO")
        try:
            printer_ok, printer_name, error_msg = batch_processor.check_printer_status()
            if not printer_ok:
                self.log_message(f"✗ 打印机检查失败: {error_msg}", "ERROR")
                raise Exception(f"打印机错误: {error_msg}")
            else:
                self.log_message(f"✓ 打印机检查通过", "SUCCESS")
                self.log_message(f"使用打印机: {printer_name}", "DETAIL")
        except Exception as e:
            self.log_message(f"✗ 打印机状态检查失败: {str(e)}", "ERROR")
            raise

        # 执行带GUI进度更新的批量打印
        try:
            self.log_message("开始执行批量打印...", "INFO")
            self.status_var.set("正在批量打印...")

            # 调用改进的批量打印方法，支持GUI进度回调
            self.optimized_batch_print_with_gui_progress(batch_processor, df, template_file)

            self.log_message("✓ 批量打印处理完成", "SUCCESS")
            self.log_message("请检查打印机输出", "INFO")

        except Exception as e:
            self.log_message(f"✗ 批量打印失败: {str(e)}", "ERROR")
            raise

    def optimized_batch_print_with_gui_progress(self, batch_processor, df, template_file):
        """支持GUI进度更新的优化批量打印方法"""
        import time
        import gc

        total_rows = len(df)
        self.log_message(f"开始处理 {total_rows} 条数据记录", "INFO")

        # 初始化统计
        all_temp_files = []
        timestamp = int(time.time())

        try:
            # 使用Excel文件管理器打开模板
            with excel_file_manager(template_file) as (wb, ws):
                self.log_message("✓ 模板文件已打开", "SUCCESS")

                # 保存原始模板状态
                self.log_message("正在保存模板原始状态...", "INFO")
                blank_template = {}
                for mapping in Config.CELL_MAPPING_RULES:
                    if mapping['excel_col'] and mapping['excel_row']:
                        cell_ref = f"{mapping['excel_col']}{mapping['excel_row']}"
                        blank_template[cell_ref] = ws[cell_ref].value
                self.log_message("✓ 模板原始状态已保存", "SUCCESS")

                # 第一阶段：按安捷通托运书.xlsx模板生成所有文件
                self.log_message("第一阶段：按安捷通托运书.xlsx模板生成所有文件...", "INFO")

                for batch_start in range(0, total_rows, Config.BATCH_SIZE):
                    # 检查是否被停止
                    if not self.is_processing:
                        self.log_message("文件生成被用户停止", "WARNING")
                        break

                    batch_end = min(batch_start + Config.BATCH_SIZE, total_rows)
                    df_batch = df.iloc[batch_start:batch_end]

                    batch_num = batch_start // Config.BATCH_SIZE + 1
                    total_batches = (total_rows + Config.BATCH_SIZE - 1) // Config.BATCH_SIZE

                    self.log_message(f"生成批次 {batch_num}/{total_batches}: 第 {batch_start+1}-{batch_end} 条数据", "INFO")

                    # 处理当前批次
                    batch_temp_files = batch_processor.process_batch_data(
                        df_batch, wb, blank_template, batch_start, timestamp
                    )
                    all_temp_files.extend(batch_temp_files)

                    # 更新进度（70-85%之间）
                    progress = 70 + (batch_end / total_rows) * 15
                    self.progress_var.set(progress)
                    self.status_var.set(f"正在生成文件 {batch_num}/{total_batches}...")

                    # 定期清理内存
                    if batch_start % (Config.BATCH_SIZE * 5) == 0:
                        gc.collect()

                self.log_message(f"✓ 文件生成完成！共生成了 {len(all_temp_files)} 个托运单文件", "SUCCESS")

                # 第二阶段：逐个打印所有生成的文件
                if all_temp_files and self.is_processing:
                    self.log_message("第二阶段：开始逐个打印所有生成的文件...", "INFO")
                    self.status_var.set("正在逐个打印文件...")
                    self.sequential_print_with_gui_progress(batch_processor, all_temp_files, total_rows)

                # 恢复模板原始状态
                self.log_message("正在恢复模板到原始状态...", "INFO")
                for cell_ref, value in blank_template.items():
                    ws[cell_ref] = value
                self.log_message("✓ 模板已恢复到原始状态", "SUCCESS")

        except Exception as e:
            self.log_message(f"✗ 批量打印过程中发生错误: {str(e)}", "ERROR")
            raise

        finally:
            # 停止窗口监控
            batch_processor.stop_window_monitor()

            # 保留临时文件
            if all_temp_files:
                self.log_message("正在保留临时文件...", "INFO")
                batch_processor.preserve_temp_files(all_temp_files)

            # 显示最终统计
            batch_processor.print_final_stats()

    def batch_send_print_jobs_with_gui_progress(self, batch_processor, temp_files, total_rows):
        """支持GUI进度更新的批量发送打印任务"""
        import queue
        import threading

        self.log_message(f"开始批量发送 {len(temp_files)} 个打印任务", "INFO")

        # 创建打印队列和结果队列
        print_queue = queue.Queue()
        results_queue = queue.Queue()

        # 启动打印工作线程
        print_threads = []
        for i in range(min(Config.MAX_WORKERS, len(temp_files))):
            thread = threading.Thread(
                target=batch_processor.print_worker,
                args=(print_queue, results_queue),
                daemon=True
            )
            thread.start()
            print_threads.append(thread)

        # 将打印任务加入队列
        for temp_file, row_index in temp_files:
            print_queue.put((temp_file, row_index, total_rows))

        # 监控打印进度
        completed = 0
        print_success = 0
        print_failed = 0

        while completed < len(temp_files) and self.is_processing:
            try:
                result = results_queue.get(timeout=1)
                status, row_index, info = result

                completed += 1
                if status == 'success':
                    print_success += 1
                    self.log_message(f"✓ 第 {row_index} 条打印任务发送成功 ({completed}/{len(temp_files)})", "SUCCESS")
                else:
                    print_failed += 1
                    self.log_message(f"✗ 第 {row_index} 条打印任务失败: {info} ({completed}/{len(temp_files)})", "ERROR")

                # 更新进度（90-100%之间）
                progress = 90 + (completed / len(temp_files)) * 10
                self.progress_var.set(progress)
                self.status_var.set(f"打印进度: {completed}/{len(temp_files)} (成功:{print_success} 失败:{print_failed})")

            except queue.Empty:
                continue

        # 停止工作线程
        for _ in print_threads:
            print_queue.put(None)

        for thread in print_threads:
            thread.join(timeout=5)

        # 更新统计
        batch_processor.monitor.update_stats(print_success=print_success, print_failed=print_failed)

        self.log_message(f"打印任务发送完成！", "SUCCESS")
        self.log_message(f"成功: {print_success} 个", "SUCCESS")
        self.log_message(f"失败: {print_failed} 个", "ERROR" if print_failed > 0 else "INFO")

        # 智能等待打印完成
        if print_success > 0:
            self.wait_for_print_completion_with_gui_progress(batch_processor, print_success, temp_files)

    def wait_for_print_completion_with_gui_progress(self, batch_processor, print_success_count, temp_files):
        """支持GUI进度更新的智能等待打印完成"""
        import time

        self.log_message("智能等待打印完成...", "INFO")

        # 计算预估打印时间
        estimated_time_per_page = 3  # 每页预估3秒
        base_wait_time = print_success_count * estimated_time_per_page
        max_wait_time = max(30, min(base_wait_time, 300))  # 最少30秒，最多5分钟

        self.log_message(f"预估打印时间: {base_wait_time} 秒", "DETAIL")
        self.log_message(f"实际等待时间: {max_wait_time} 秒", "DETAIL")

        # 分阶段等待，每10秒检查一次
        check_interval = 10
        total_checks = max_wait_time // check_interval

        for check in range(total_checks):
            if not self.is_processing:
                self.log_message("等待打印完成被用户停止", "WARNING")
                break

            current_time = (check + 1) * check_interval
            remaining_time = max_wait_time - current_time

            self.status_var.set(f"等待打印完成: {current_time}/{max_wait_time}秒 (剩余: {remaining_time}秒)")
            self.log_message(f"等待进度: {current_time}/{max_wait_time}秒 (剩余: {remaining_time}秒)", "INFO")

            # 检查文件是否仍被占用
            locked_count = batch_processor.check_temp_files_status(temp_files)
            if locked_count == 0:
                self.log_message("✓ 所有文件已释放，打印可能已完成", "SUCCESS")
                break
            else:
                self.log_message(f"⏳ 还有 {locked_count} 个文件被占用，继续等待...", "INFO")

            # 分段等待，避免阻塞GUI
            for i in range(check_interval):
                if not self.is_processing:
                    break
                time.sleep(1)

        self.log_message("智能等待完成！", "SUCCESS")

    def sequential_print_with_gui_progress(self, batch_processor, temp_files, total_rows):
        """支持GUI进度更新的逐个打印方法"""
        import time

        self.log_message(f"准备逐个打印 {len(temp_files)} 个文件...", "INFO")
        self.log_message("打印方式：按顺序逐个打印，确保打印质量", "INFO")

        print_success = 0
        print_failed = 0

        for i, (temp_file, row_index) in enumerate(temp_files, 1):
            # 检查是否被停止
            if not self.is_processing:
                self.log_message("逐个打印被用户停止", "WARNING")
                break

            try:
                self.log_message(f"正在打印第 {i}/{len(temp_files)} 个文件...", "INFO")
                self.log_message(f"文件: {os.path.basename(temp_file)}", "DETAIL")
                self.log_message(f"数据行: 第 {row_index} 条", "DETAIL")

                # 更新进度（85-100%之间）
                progress = 85 + (i / len(temp_files)) * 15
                self.progress_var.set(progress)
                self.status_var.set(f"正在打印 {i}/{len(temp_files)} (成功:{print_success} 失败:{print_failed})")

                # 等待文件可访问
                if not batch_processor.wait_for_file_access(temp_file, timeout=10, operation="打印"):
                    self.log_message(f"文件被占用，跳过打印", "WARNING")
                    print_failed += 1
                    continue

                # 执行打印
                success = batch_processor.silent_print_excel(temp_file)

                if success:
                    print_success += 1
                    self.log_message(f"✓ 打印成功 ({print_success}/{len(temp_files)})", "SUCCESS")
                else:
                    print_failed += 1
                    self.log_message(f"✗ 打印失败 ({print_failed}/{len(temp_files)})", "ERROR")

                # 缩短打印间隔，快速释放资源
                if i < len(temp_files) and self.is_processing:  # 不是最后一个文件且未被停止
                    self.log_message(f"等待 0.5 秒后继续下一个...", "DETAIL")
                    # 分段等待，避免阻塞GUI
                    for j in range(5):  # 每0.1秒检查一次，总共0.5秒
                        if not self.is_processing:
                            break
                        time.sleep(0.1)

            except Exception as e:
                print_failed += 1
                self.log_message(f"✗ 打印第 {i} 个文件时出错: {str(e)}", "ERROR")

        # 更新统计
        batch_processor.monitor.update_stats(print_success=print_success, print_failed=print_failed)

        self.log_message("逐个打印完成！", "SUCCESS")
        self.log_message(f"总文件数: {len(temp_files)}", "INFO")
        self.log_message(f"打印成功: {print_success}", "SUCCESS")
        self.log_message(f"打印失败: {print_failed}", "ERROR" if print_failed > 0 else "INFO")
        success_rate = (print_success/len(temp_files)*100) if temp_files else 0
        self.log_message(f"成功率: {success_rate:.1f}%", "SUCCESS" if success_rate > 80 else "WARNING")

        if print_success > 0:
            self.log_message(f"✅ 已成功发送 {print_success} 个打印任务到打印机", "SUCCESS")
            self.log_message("请检查打印机输出，确认所有文件都已正确打印", "INFO")

    def check_processing_status(self):
        """检查处理线程状态并更新界面"""
        try:
            if not self.is_processing:
                return
                
            if self.processing_thread and not self.processing_thread.is_alive():
                # 处理线程已结束，重置界面状态
                self.log_message("处理线程已完成", "INFO")
                self.reset_processing_state()
            else:
                # 线程仍在运行，继续检查
                self.root.after(1000, self.check_processing_status)
        except Exception as e:
            self.log_message(f"检查处理状态时出错: {str(e)}", "ERROR")
            self.reset_processing_state()

    def reset_processing_state(self):
        """重置处理状态和界面"""
        try:
            self.is_processing = False
            
            # 恢复按钮状态
            if hasattr(self, 'process_btn') and self.process_btn is not None:
                self.process_btn.config(state="normal")
            if hasattr(self, 'stop_btn') and self.stop_btn is not None:
                self.stop_btn.config(state="disabled")
            if hasattr(self, 'browse_btn') and self.browse_btn is not None:
                self.browse_btn.config(state="normal")
                
            # 更新状态文本
            if hasattr(self, 'status_var') and self.status_var is not None:
                current_status = self.status_var.get()
                if current_status in ["正在处理...", "处理中..."]:
                    self.status_var.set("就绪")
        except Exception as e:
            print(f"重置处理状态时出错: {str(e)}")

    def finish_processing(self):
        """完成处理，恢复界面状态"""
        self.reset_processing_state()

if __name__ == "__main__":
    # 检查是否以GUI模式运行
    if len(sys.argv) > 1 and sys.argv[1] == "--gui":
        # 启动GUI界面
        app = ShippingOrderGUI()
        app.root.mainloop()
    else:
        # 命令行模式
        main()