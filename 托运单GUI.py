#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
托运单处理GUI启动器
简化的GUI界面启动程序
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    # 导入主程序中的GUI类
    from 处理托运单_优化版 import ShippingOrderGUI
    
    def main():
        """启动GUI界面"""
        print("正在启动托运单批量处理系统...")
        
        # 创建并启动GUI应用
        app = ShippingOrderGUI()
        
        # 设置窗口居中
        app.root.update_idletasks()
        width = app.root.winfo_width()
        height = app.root.winfo_height()
        x = (app.root.winfo_screenwidth() // 2) - (width // 2)
        y = (app.root.winfo_screenheight() // 2) - (height // 2)
        app.root.geometry(f'{width}x{height}+{x}+{y}')
        
        print("GUI界面已启动")
        
        # 运行主循环
        app.root.mainloop()
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保 '处理托运单_优化版.py' 文件在同一目录中")
    input("按回车键退出...")
except Exception as e:
    print(f"启动GUI时发生错误: {e}")
    input("按回车键退出...")
