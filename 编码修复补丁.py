#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码修复补丁 - 专门修复Unicode编码问题
解决PowerShell和VBScript打印时的编码错误
"""

import subprocess
import tempfile
import os
import time

class EncodingFixedPrintManager:
    """修复编码问题的打印管理器"""
    
    def __init__(self):
        self.temp_files = []
        print("🔧 编码修复打印管理器初始化完成")
    
    def safe_get_short_path(self, file_path):
        """安全获取短路径，避免中文字符问题"""
        try:
            import win32api
            return win32api.GetShortPathName(os.path.abspath(file_path))
        except:
            # 如果获取短路径失败，返回原路径并进行转义
            return os.path.abspath(file_path).replace('\\', '\\\\')
    
    def powershell_print_fixed(self, file_path):
        """修复编码问题的PowerShell打印"""
        try:
            print("🔄 PowerShell打印（编码修复版）...")
            
            # 获取安全的文件路径
            safe_path = self.safe_get_short_path(file_path)
            
            # 创建PowerShell脚本，强制使用UTF-8编码
            ps_script_content = f'''
# 强制设置编码为UTF-8
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

try {{
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    $excel.ScreenUpdating = $false
    
    $workbook = $excel.Workbooks.Open("{safe_path}", 0, $true)
    $workbook.PrintOut()
    $workbook.Close($false)
    $excel.Quit()
    
    # 清理COM对象
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    [System.GC]::Collect()
    
    Write-Output "SUCCESS"
}} catch {{
    Write-Output "ERROR: $($_.Exception.Message)"
    exit 1
}}
'''
            
            # 写入临时PowerShell文件，使用UTF-8 BOM编码
            with tempfile.NamedTemporaryFile(mode='w', suffix='.ps1', delete=False, encoding='utf-8-sig') as f:
                f.write(ps_script_content)
                ps_file = f.name
            
            try:
                # 执行PowerShell，明确指定编码参数
                result = subprocess.run(
                    [
                        'powershell.exe', 
                        '-WindowStyle', 'Hidden', 
                        '-NoProfile', 
                        '-NonInteractive', 
                        '-NoLogo', 
                        '-ExecutionPolicy', 'Bypass',
                        '-OutputFormat', 'Text',
                        '-File', ps_file
                    ],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',      # 明确指定UTF-8编码
                    errors='replace',      # 替换无法解码的字符
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                
                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ PowerShell打印成功（编码修复）")
                    return True
                else:
                    # 安全处理错误输出
                    stdout_safe = self.safe_decode_output(result.stdout)
                    stderr_safe = self.safe_decode_output(result.stderr)
                    print(f"⚠️ PowerShell打印失败")
                    print(f"   返回码: {result.returncode}")
                    print(f"   输出: {stdout_safe}")
                    print(f"   错误: {stderr_safe}")
                    return False
                    
            finally:
                # 清理临时文件
                try:
                    time.sleep(0.5)
                    os.unlink(ps_file)
                except:
                    pass
                    
        except Exception as e:
            print(f"⚠️ PowerShell打印异常: {e}")
            return False
    
    def vbscript_print_fixed(self, file_path):
        """修复编码问题的VBScript打印"""
        try:
            print("🔄 VBScript打印（编码修复版）...")
            
            # 获取安全的文件路径
            safe_path = self.safe_get_short_path(file_path)
            
            # 创建VBScript内容
            vbs_content = f'''
On Error Resume Next

Set objExcel = CreateObject("Excel.Application")
If Err.Number <> 0 Then
    WScript.Echo "ERROR: Cannot create Excel"
    WScript.Quit 1
End If

objExcel.Visible = False
objExcel.DisplayAlerts = False
objExcel.ScreenUpdating = False

Set objWorkbook = objExcel.Workbooks.Open("{safe_path}", 0, True)
If Err.Number <> 0 Then
    objExcel.Quit
    WScript.Echo "ERROR: Cannot open file"
    WScript.Quit 1
End If

objWorkbook.PrintOut
objWorkbook.Close False
objExcel.Quit

Set objWorkbook = Nothing
Set objExcel = Nothing

WScript.Echo "SUCCESS"
'''
            
            # 写入临时VBS文件，使用ANSI编码（cscript默认编码）
            with tempfile.NamedTemporaryFile(mode='w', suffix='.vbs', delete=False, encoding='gbk') as f:
                f.write(vbs_content)
                vbs_file = f.name
            
            try:
                # 执行VBScript，指定正确的编码
                result = subprocess.run(
                    ['cscript.exe', '//NoLogo', '//B', vbs_file],
                    capture_output=True,
                    text=True,
                    encoding='gbk',        # 使用GBK编码读取cscript输出
                    errors='replace',      # 替换无法解码的字符
                    timeout=30,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                
                if result.returncode == 0 and "SUCCESS" in result.stdout:
                    print("✅ VBScript打印成功（编码修复）")
                    return True
                else:
                    # 安全处理错误输出
                    stdout_safe = self.safe_decode_output(result.stdout)
                    stderr_safe = self.safe_decode_output(result.stderr)
                    print(f"⚠️ VBScript打印失败")
                    print(f"   输出: {stdout_safe}")
                    print(f"   错误: {stderr_safe}")
                    return False
                    
            finally:
                # 清理临时文件
                try:
                    time.sleep(0.5)
                    os.unlink(vbs_file)
                except:
                    pass
                    
        except Exception as e:
            print(f"⚠️ VBScript打印异常: {e}")
            return False
    
    def safe_decode_output(self, output):
        """安全解码输出，避免编码错误"""
        if not output:
            return "无输出"
        
        try:
            if isinstance(output, bytes):
                # 尝试多种编码
                for encoding in ['utf-8', 'gbk', 'cp936', 'latin1']:
                    try:
                        return output.decode(encoding)
                    except UnicodeDecodeError:
                        continue
                # 如果所有编码都失败，使用错误替换
                return output.decode('utf-8', errors='replace')
            else:
                return str(output).strip()
        except:
            return "[编码错误，无法显示]"
    
    def print_excel_file(self, file_path, printer_name=None):
        """主打印方法，使用编码修复版本"""
        try:
            print(f"🖨️ 开始打印: {os.path.basename(file_path)}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False
            
            # 尝试PowerShell打印（编码修复版）
            if self.powershell_print_fixed(file_path):
                return True
            
            # 如果PowerShell失败，尝试VBScript打印（编码修复版）
            if self.vbscript_print_fixed(file_path):
                return True
            
            print("❌ 所有打印方法都失败了")
            return False
            
        except Exception as e:
            print(f"❌ 打印过程异常: {e}")
            return False
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            for temp_file in self.temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            self.temp_files.clear()
            print("🧹 临时文件已清理")
        except Exception as e:
            print(f"⚠️ 清理临时文件时出错: {e}")

# 测试函数
def test_encoding_fix():
    """测试编码修复"""
    print("🧪 测试编码修复功能...")
    
    # 创建修复版打印管理器
    print_manager = EncodingFixedPrintManager()
    
    # 测试路径处理
    test_path = "C:\\测试文件\\托运单_测试.xlsx"
    safe_path = print_manager.safe_get_short_path(test_path)
    print(f"原路径: {test_path}")
    print(f"安全路径: {safe_path}")
    
    # 测试输出解码
    test_bytes = "测试输出".encode('gbk')
    decoded = print_manager.safe_decode_output(test_bytes)
    print(f"解码测试: {decoded}")
    
    print("✅ 编码修复测试完成")

if __name__ == "__main__":
    test_encoding_fix()
